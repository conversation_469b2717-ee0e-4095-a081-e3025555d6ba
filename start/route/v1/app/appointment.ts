/*
|--------------------------------------------------------------------------
| Appointment Routes
|--------------------------------------------------------------------------
|
| Routes for managing appointments
|
*/

import { middleware } from '#start/kernel'
import router from '@adonisjs/core/services/router'

const AppointmentController = () => import('#controllers/app/store-service/appointment_controller')

export default function appointmentRoutes() {
  router
    .group(() => {
      router
        .group(() => {
          router.post('/', [AppointmentController, 'create'])
          router.get('/:id', [AppointmentController, 'show'])
          router.put('/:id', [AppointmentController, 'update'])
          router.put('/:id/status', [AppointmentController, 'updateStatus'])
          router.delete('/:id', [AppointmentController, 'delete'])
          router.get('/store/:storeId', [AppointmentController, 'getByStore'])
          router.get('/customer/:customerId', [AppointmentController, 'getByCustomer'])
        })
        .use(middleware.auth())
    })
    .prefix('appointments')
}
