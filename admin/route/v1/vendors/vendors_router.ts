import { middleware } from "#start/kernel"
import router from "@adonisjs/core/services/router"

const AdminVendorController = () => import("#adminControllers/vendors/admin_vendor_controller");

export default function adminVendorRoutes() {
  router
    .group(() => {
      router.get('vendors/settings', [AdminVendorController, 'settings'])
      router.resource('vendors', AdminVendorController)
    })
    .use(middleware.auth({ guards: ['jwt_admin'] as any }))
}