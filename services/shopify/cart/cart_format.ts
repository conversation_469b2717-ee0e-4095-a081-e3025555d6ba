import { DateTime } from 'luxon'
import { ICart, IDiscountAllocation } from './type.js'

export const ORDER_EXPIRE_AFTER = 5 * 60 * 1000 // 5 minutes

export const formatCart = async (cart: any): Promise<ICart> => {
  const amount = cart.attributes?.find((attribute: any) => attribute.key === 'amount')?.value || 0
  const rawDiscountAllocations = cart.discountAllocations.map(({ discountedAmount }: any) => {
    return {
      amount: Number(discountedAmount.amount),
    }
  })

  const rawTotalDiscountAmount = rawDiscountAllocations.reduce(
    (acc: number, discountAllocation: IDiscountAllocation) => acc + discountAllocation.amount,
    0
  )
  const discountAllocations = cart.discountAllocations.map(({ discountApplication }: any) => {
    let result = rawTotalDiscountAmount
    const percentage = discountApplication.value.percentage
    if (percentage) {
      result = (amount * percentage) / 100
    }

    return {
      amount: result,
    }
  })
  const totalDiscountValue = discountAllocations?.[0]?.amount || 0

  const totalAmount = Number(cart.cost.totalAmount?.amount || 0)
  const subtotalAmount = Number(cart.cost.subtotalAmount?.amount || 0)
  const totalTaxAmount = Number(cart.cost.totalTaxAmount?.amount || 0)
  const totalShipAmount = totalAmount + rawTotalDiscountAmount - subtotalAmount - totalTaxAmount
  const discountCodes = cart.discountCodes
    .filter((discountCode: any) => discountCode.applicable)
    .map((discountCode: any) => discountCode.code)
  const totalDiscountAmount = totalDiscountValue + (subtotalAmount - amount)
  return {
    id: cart.id,
    checkoutUrl: cart.checkoutUrl,
    lineItems: cart.lineItems,
    discountCodes,
    note: cart.note,
    discountAllocations,
    totalAmount: subtotalAmount + totalTaxAmount + totalShipAmount - totalDiscountAmount,
    subtotalAmount,
    totalTaxAmount,
    totalShipAmount,
    totalDiscountAmount,
    expiredAt: DateTime.fromJSDate(new Date(cart.createdAt))
      .plus({ milliseconds: ORDER_EXPIRE_AFTER })
      .toUTC()
      .toString(),
  }
}
export const formatOrderCart = async (order: any): Promise<ICart> => {
  const discountAllocations = order?.platformDiscounts?.map(
    (d: any) => ({ amount: Number(d.totalAmount.amount || 0) }) as IDiscountAllocation
  )
  const discountCodes = order?.platformDiscounts?.map((d: any) => d.code).filter(Boolean)
  const totalDiscountAmount = Number(order.totalDiscountsSet?.shopMoney?.amount || 0)
  const subtotalAmount = Number(order.totalLineItemsPriceSet?.shopMoney?.amount || 0)
  const totalTaxAmount = Number(order.totalTaxSet?.shopMoney?.amount || 0)
  let totalAmount = Number(order.totalPriceSet?.shopMoney?.amount || 0)
  let totalShipAmount = Number(order.totalShippingPriceSet?.shopMoney?.amount || 0)

  return {
    id: order.id,
    checkoutUrl: order.invoiceUrl,
    lineItems: order.lineItems,
    discountCodes,
    note: order.note2 || order.note || '',
    discountAllocations,
    totalAmount,
    subtotalAmount,
    totalTaxAmount,
    totalShipAmount,
    totalDiscountAmount,
    expiredAt: DateTime.fromJSDate(new Date(order.createdAt))
      .plus({ milliseconds: ORDER_EXPIRE_AFTER })
      .toUTC()
      .toString(),
  }
}
