import { BaseSchema } from '@adonisjs/lucid/schema'

export default class extends BaseSchema {
  protected tableName = 'zn_vendors'

  async up() {
    this.schema.alterTable(this.tableName, (table) => {
      table.float('commissionRate').defaultTo(0)
      table.float('fixedCommissionAmount').defaultTo(0)
    })
  }

  async down() {
    this.schema.alterTable(this.tableName, (table) => {
      table.dropColumn('commissionRate')
      table.dropColumn('fixedCommissionAmount')
    })
  }
}