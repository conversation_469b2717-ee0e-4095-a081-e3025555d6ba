import { BaseSchema } from '@adonisjs/lucid/schema'

export default class extends BaseSchema {
  protected tableName = 'zn_bundle_collections'

  async up() {
    this.schema.createTable(this.tableName, (table) => {
      table.uuid('id').primary().index()
      table.string('fastBundleId')
      table.uuid('bundleId').notNullable().index()
      table.string('title').notNullable()
      table.string('sectionTitle').nullable()
      table.string('sectionDescription').nullable()
      table.string('image').nullable()
      table.integer('productCount').defaultTo(0).unsigned()
      table.boolean('showInPage').defaultTo(true)
      table.boolean('plusOneQuantity').defaultTo(false)
      table.integer('quantity').defaultTo(0).unsigned()
      table.integer('maxQuantity').defaultTo(0).unsigned()
      table.boolean('ignoresDiscount').defaultTo(false)
      table.boolean('isActive').defaultTo(true)
      table.timestamp('createdAt', { useTz: true }).notNullable().defaultTo(this.now())
      table.timestamp('updatedAt', { useTz: true }).notNullable().defaultTo(this.now())
      table.timestamp('deletedAt', { useTz: true }).nullable().index()
    })
  }

  async down() {
    this.schema.dropTable(this.tableName)
  }
}
