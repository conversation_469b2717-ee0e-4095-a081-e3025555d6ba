import { BaseSchema } from '@adonisjs/lucid/schema'

export default class extends BaseSchema {
  protected tableName = 'zn_bundle_collection_items'

  async up() {
    this.schema.createTable(this.tableName, (table) => {
      table.uuid('id').primary().index()
      table.uuid('bundleId').notNullable().index()
      table.uuid('bundleCollectionId').notNullable().index()
      table.string('fastBundleId')
      table.string('title').notNullable()
      table.string('image').nullable()

      table.timestamp('createdAt', { useTz: true }).notNullable().defaultTo(this.now())
      table.timestamp('updatedAt', { useTz: true }).notNullable().defaultTo(this.now())
      table.timestamp('deletedAt', { useTz: true }).nullable().index()
    })
  }

  async down() {
    this.schema.dropTable(this.tableName)
  }
}
