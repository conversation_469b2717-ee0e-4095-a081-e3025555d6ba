import { BaseSchema } from '@adonisjs/lucid/schema'

export default class extends BaseSchema {
  protected tableName = 'zn_vendors'

  async up() {
    this.schema.alterTable(this.tableName, (table) => {
      table.enu('registrationStatus', ['pending', 'approved', 'rejected']).defaultTo('pending').notNullable()
      table.string('rejectionReason').nullable().after('registrationStatus')
    })
  }

  async down() {
    this.schema.alterTable(this.tableName, (table) => {
      table.dropColumn('registrationStatus')
      table.dropColumn('rejectionReason')
    })
  }
}