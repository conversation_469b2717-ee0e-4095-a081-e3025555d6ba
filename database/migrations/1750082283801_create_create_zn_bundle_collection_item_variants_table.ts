import { BaseSchema } from '@adonisjs/lucid/schema'

export default class extends BaseSchema {
  protected tableName = 'zn_bundle_collection_item_variants'

  async up() {
    this.schema.createTable(this.tableName, (table) => {
      table.uuid('bundleCollectionItemId')
      table.uuid('variantId')
      table.primary(['bundleCollectionItemId', 'variantId'])
    })
  }

  async down() {
    this.schema.dropTable(this.tableName)
  }
}
