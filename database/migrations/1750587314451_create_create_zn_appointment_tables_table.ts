import { BaseSchema } from '@adonisjs/lucid/schema'
import { EAppointmentStatus } from '#models/store_service/zn_appointment'

export default class extends BaseSchema {
  protected appointmentTableName = 'zn_appointments'
  protected appointmentServiceTableName = 'zn_appointment_services'
  protected appointmentPackageTableName = 'zn_appointment_packages'

  async up() {
    this.schema.createTable(this.appointmentTableName, (table) => {
      table.uuid('id').primary()
      table.uuid('storeId').references('id').inTable('zn_stores').onDelete('CASCADE')
      table.uuid('customerId').references('id').inTable('zn_users').onDelete('SET NULL').nullable()
      table.enum('status', Object.values(EAppointmentStatus)).defaultTo(EAppointmentStatus.BOOKED)
      table.timestamp('startTime').notNullable()
      table.timestamp('endTime').notNullable()
      table.text('notes').nullable()
      table.uuid('taxId').references('id').inTable('zn_store_taxes').onDelete('SET NULL').nullable()
      table.decimal('subtotal', 10, 2).defaultTo(0)
      table.decimal('discount', 10, 2).defaultTo(0)
      table.decimal('taxAmount', 10, 2).defaultTo(0)
      table.decimal('tipAmount', 10, 2).defaultTo(0)
      table.decimal('total', 10, 2).defaultTo(0)
      table.timestamp('createdAt', { useTz: true }).notNullable().defaultTo(this.now())
      table.timestamp('updatedAt', { useTz: true }).notNullable().defaultTo(this.now())
      table.timestamp('deletedAt', { useTz: true }).nullable()
    })

    this.schema.createTable(this.appointmentServiceTableName, (table) => {
      table.uuid('id').primary()
      table.uuid('appointmentId').references('id').inTable('zn_appointments').onDelete('CASCADE')
      table
        .uuid('serviceId')
        .references('id')
        .inTable('zn_store_services')
        .onDelete('SET NULL')
        .nullable()
      table.decimal('price', 10, 2).defaultTo(0)
      table.integer('duration').defaultTo(0)

      table.timestamp('createdAt', { useTz: true }).notNullable().defaultTo(this.now())
      table.timestamp('updatedAt', { useTz: true }).notNullable().defaultTo(this.now())
    })

    this.schema.createTable(this.appointmentPackageTableName, (table) => {
      table.uuid('id').primary()
      table.uuid('appointmentId').references('id').inTable('zn_appointments').onDelete('CASCADE')
      table
        .uuid('packageId')
        .references('id')
        .inTable('zn_store_packages')
        .onDelete('SET NULL')
        .nullable()
      table.decimal('price', 10, 2).defaultTo(0)

      table.timestamp('createdAt', { useTz: true }).notNullable().defaultTo(this.now())
      table.timestamp('updatedAt', { useTz: true }).notNullable().defaultTo(this.now())
    })
  }

  async down() {
    this.schema.dropTable(this.appointmentPackageTableName)
    this.schema.dropTable(this.appointmentServiceTableName)
    this.schema.dropTable(this.appointmentTableName)
  }
}
