import { BaseCommand } from '@adonisjs/core/ace'
import type { CommandOptions } from '@adonisjs/core/types/ace'
import { RESOURCE_TYPE } from '#constants/like_comment_resource'
import ZnUserLikeResource from '#models/zn_user_like_resource'
import ZnResourceInteracts from '#models/zn_resource_interacts'
import db from '@adonisjs/lucid/services/db'

interface SyncIssue {
  resourceId: string
  resourceType: string
  actualLikeCount: number
  storedLikeCount: number
  actualUserCount: number
  storedUserCount: number
  issue: string
}

export default class DiagnoseLikeSync extends BaseCommand {
  static commandName = 'diagnose:like-sync'
  static description = 'Diagnose synchronization issues between like counts and user data'

  static options: CommandOptions = {
    startApp: true,
  }

  async run() {
    this.logger.info('🔍 Diagnosing Like/Dislike Synchronization Issues')
    
    const resourceType = this.parsed.flags.resource as string
    const limit = Number(this.parsed.flags.limit) || 50
    const showDetails = this.parsed.flags.details as boolean

    try {
      if (!resourceType || resourceType === 'all') {
        await this.diagnoseAllResourceTypes(limit, showDetails)
      } else {
        await this.diagnoseSpecificResourceType(resourceType, limit, showDetails)
      }

      this.logger.success('✅ Diagnosis completed!')
    } catch (error) {
      this.logger.error('❌ Error during diagnosis:', error)
      throw error
    }
  }

  private async diagnoseAllResourceTypes(limit: number, showDetails: boolean) {
    this.logger.info('📊 Diagnosing all resource types...')
    
    const issues: SyncIssue[] = []
    
    // Check Posts
    const postIssues = await this.checkResourceType(RESOURCE_TYPE.POST, limit)
    issues.push(...postIssues)
    
    // Check Streams
    const streamIssues = await this.checkResourceType(RESOURCE_TYPE.STREAM, limit)
    issues.push(...streamIssues)
    
    // Check Product Reviews
    const reviewIssues = await this.checkResourceType(RESOURCE_TYPE.PRODUCT_REVIEW, limit)
    issues.push(...reviewIssues)

    await this.displayResults(issues, showDetails)
    await this.showSummaryStats()
  }

  private async diagnoseSpecificResourceType(resourceType: string, limit: number, showDetails: boolean) {
    let actualResourceType: string
    
    switch (resourceType.toLowerCase()) {
      case 'post':
      case 'posts':
        actualResourceType = RESOURCE_TYPE.POST
        break
      case 'stream':
      case 'streams':
        actualResourceType = RESOURCE_TYPE.STREAM
        break
      case 'product_review':
      case 'product-review':
      case 'reviews':
        actualResourceType = RESOURCE_TYPE.PRODUCT_REVIEW
        break
      default:
        this.logger.error(`❌ Unknown resource type: ${resourceType}`)
        this.logger.info('Available types: post, stream, product_review, all')
        return
    }

    this.logger.info(`📊 Diagnosing ${actualResourceType} resources...`)
    
    const issues = await this.checkResourceType(actualResourceType, limit)
    await this.displayResults(issues, showDetails)
  }

  private async checkResourceType(resourceType: string, limit: number): Promise<SyncIssue[]> {
    this.logger.info(`🔍 Checking ${resourceType} synchronization...`)
    
    const issues: SyncIssue[] = []

    // Get all resources that have either likes or interaction records
    const query = `
      SELECT DISTINCT resourceId 
      FROM (
        SELECT resourceId FROM zn_users_like_resources 
        WHERE resourceType = ? AND deletedAt IS NULL
        UNION
        SELECT resourceId FROM zn_resource_interacts 
        WHERE likeCount > 0 OR JSON_LENGTH(COALESCE(likeUsers, '[]')) > 0
      ) combined
      LIMIT ?
    `
    
    const resourceIds = await db.rawQuery(query, [resourceType, limit])
    const ids = resourceIds[0].map((row: any) => row.resourceId)
    
    this.logger.info(`Found ${ids.length} ${resourceType} resources to check`)

    for (const resourceId of ids) {
      // Get actual like count from source of truth
      const actualLikeCount = await db
        .from(ZnUserLikeResource.table)
        .where('resourceType', resourceType)
        .where('resourceId', resourceId)
        .whereNull('deletedAt')
        .count('* as count')
        .first()

      const actualCount = Number(actualLikeCount?.count || 0)

      // Get actual users who liked
      const actualUsers = await db
        .from(ZnUserLikeResource.table)
        .where('resourceType', resourceType)
        .where('resourceId', resourceId)
        .whereNull('deletedAt')
        .select('userId')

      const actualUserCount = actualUsers.length

      // Get stored interaction data
      const storedInteraction = await db
        .from(ZnResourceInteracts.table)
        .where('resourceId', resourceId)
        .select('likeCount', 'likeUsers')
        .first()

      const storedCount = Number(storedInteraction?.likeCount || 0)
      const storedUsers = storedInteraction?.likeUsers ? 
        (typeof storedInteraction.likeUsers === 'string' ? 
          JSON.parse(storedInteraction.likeUsers) : 
          storedInteraction.likeUsers) : []
      const storedUserCount = Array.isArray(storedUsers) ? storedUsers.length : 0

      // Check for issues
      const hasCountMismatch = actualCount !== storedCount
      const hasUserMismatch = actualUserCount !== storedUserCount
      const hasOrphanedRecord = actualCount === 0 && storedCount > 0
      const hasMissingRecord = actualCount > 0 && !storedInteraction

      if (hasCountMismatch || hasUserMismatch || hasOrphanedRecord || hasMissingRecord) {
        let issue = ''
        if (hasCountMismatch) issue += `Count mismatch (actual: ${actualCount}, stored: ${storedCount}). `
        if (hasUserMismatch) issue += `User count mismatch (actual: ${actualUserCount}, stored: ${storedUserCount}). `
        if (hasOrphanedRecord) issue += 'Orphaned interaction record. '
        if (hasMissingRecord) issue += 'Missing interaction record. '

        issues.push({
          resourceId,
          resourceType,
          actualLikeCount: actualCount,
          storedLikeCount: storedCount,
          actualUserCount,
          storedUserCount,
          issue: issue.trim()
        })
      }
    }

    return issues
  }

  private async displayResults(issues: SyncIssue[], showDetails: boolean) {
    if (issues.length === 0) {
      this.logger.success('✅ No synchronization issues found!')
      return
    }

    this.logger.warn(`⚠️  Found ${issues.length} synchronization issues:`)
    this.logger.info('')

    // Group issues by type
    const issuesByType = issues.reduce((acc, issue) => {
      if (!acc[issue.resourceType]) acc[issue.resourceType] = []
      acc[issue.resourceType].push(issue)
      return acc
    }, {} as Record<string, SyncIssue[]>)

    for (const [resourceType, resourceIssues] of Object.entries(issuesByType)) {
      this.logger.info(`📋 ${resourceType.toUpperCase()} Issues (${resourceIssues.length}):`)
      
      if (showDetails) {
        resourceIssues.forEach((issue, index) => {
          this.logger.info(`  ${index + 1}. Resource ID: ${issue.resourceId}`)
          this.logger.info(`     Issue: ${issue.issue}`)
          this.logger.info(`     Actual likes: ${issue.actualLikeCount} (${issue.actualUserCount} users)`)
          this.logger.info(`     Stored likes: ${issue.storedLikeCount} (${issue.storedUserCount} users)`)
          this.logger.info('')
        })
      } else {
        // Show summary
        const countMismatches = resourceIssues.filter(i => i.actualLikeCount !== i.storedLikeCount).length
        const userMismatches = resourceIssues.filter(i => i.actualUserCount !== i.storedUserCount).length
        const orphaned = resourceIssues.filter(i => i.actualLikeCount === 0 && i.storedLikeCount > 0).length
        const missing = resourceIssues.filter(i => i.actualLikeCount > 0 && i.storedLikeCount === 0).length

        this.logger.info(`  - Count mismatches: ${countMismatches}`)
        this.logger.info(`  - User mismatches: ${userMismatches}`)
        this.logger.info(`  - Orphaned records: ${orphaned}`)
        this.logger.info(`  - Missing records: ${missing}`)
        this.logger.info('')
      }
    }

    this.logger.info('💡 To fix these issues, run:')
    this.logger.info('   node ace sync:like-interactions')
    this.logger.info('   or for specific resource type:')
    this.logger.info('   node ace sync:like-interactions --resource=post')
  }

  private async showSummaryStats() {
    this.logger.info('📊 Overall Statistics:')
    
    // Total likes by resource type
    const likeStats = await db
      .from(ZnUserLikeResource.table)
      .select('resourceType')
      .count('* as count')
      .whereNull('deletedAt')
      .groupBy('resourceType')

    likeStats.forEach(stat => {
      this.logger.info(`  ${stat.resourceType}: ${stat.count} total likes`)
    })

    // Total interaction records
    const interactionCount = await db
      .from(ZnResourceInteracts.table)
      .count('* as count')
      .first()

    this.logger.info(`  Interaction records: ${interactionCount?.count || 0}`)
    
    // Records with like data
    const recordsWithLikes = await db
      .from(ZnResourceInteracts.table)
      .where('likeCount', '>', 0)
      .count('* as count')
      .first()

    this.logger.info(`  Records with likes: ${recordsWithLikes?.count || 0}`)
  }

  // Add command flags
  static flags = {
    resource: {
      type: 'string' as const,
      description: 'Specific resource type to diagnose (post, stream, product_review, or all)',
      default: 'all'
    },
    limit: {
      type: 'number' as const,
      description: 'Maximum number of resources to check per type',
      default: 50
    },
    details: {
      type: 'boolean' as const,
      description: 'Show detailed information for each issue',
      default: false
    }
  }
}
