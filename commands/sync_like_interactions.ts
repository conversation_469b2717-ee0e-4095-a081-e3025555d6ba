import { BaseCommand } from '@adonisjs/core/ace'
import type { CommandOptions } from '@adonisjs/core/types/ace'
import { RESOURCE_TYPE } from '#constants/like_comment_resource'
import { TRACKING_ACTION } from '#constants/tracking'
import SyncInteracts<PERSON><PERSON>Job from '#jobs/sync_interacts_data_job'
import ZnPost from '#models/zn_post'
import ZnStream from '#models/zn_stream'
import ZnUserLikeResource from '#models/zn_user_like_resource'
import ZnResourceInteracts from '#models/zn_resource_interacts'
import { TrackingService } from '#services/tracking_service'
import queue from '@rlanz/bull-queue/services/main'
import db from '@adonisjs/lucid/services/db'

export default class SyncLikeInteractions extends BaseCommand {
  static commandName = 'sync:like-interactions'
  static description = 'Sync all like/dislike interaction data to fix count and users synchronization issues'

  static options: CommandOptions = {
    startApp: true,
  }

  private trackingService = new TrackingService()

  async run() {
    this.logger.info('🚀 Starting Like/Dislike Interactions Sync')
    
    // Get command line arguments for selective sync
    const resourceType = this.parsed.flags.resource as string
    const batchSize = Number(this.parsed.flags.batchSize) || 100
    const dryRun = this.parsed.flags.dryRun as boolean

    if (dryRun) {
      this.logger.info('🔍 DRY RUN MODE - No changes will be made')
    }

    try {
      if (!resourceType || resourceType === 'all') {
        await this.syncAllResourceTypes(batchSize, dryRun)
      } else {
        await this.syncSpecificResourceType(resourceType, batchSize, dryRun)
      }

      this.logger.success('✅ Like/Dislike Interactions Sync completed successfully!')
    } catch (error) {
      this.logger.error('❌ Error during sync:', error)
      throw error
    }
  }

  private async syncAllResourceTypes(batchSize: number, dryRun: boolean) {
    this.logger.info('📊 Syncing all resource types...')
    
    // Sync Posts
    await this.syncPosts(batchSize, dryRun)
    
    // Sync Streams  
    await this.syncStreams(batchSize, dryRun)
    
    // Sync Product Reviews (if any exist)
    await this.syncProductReviews(batchSize, dryRun)

    // Clean up orphaned interaction records
    await this.cleanupOrphanedRecords(dryRun)
  }

  private async syncSpecificResourceType(resourceType: string, batchSize: number, dryRun: boolean) {
    this.logger.info(`📊 Syncing ${resourceType} resources...`)
    
    switch (resourceType.toLowerCase()) {
      case 'post':
      case 'posts':
        await this.syncPosts(batchSize, dryRun)
        break
      case 'stream':
      case 'streams':
        await this.syncStreams(batchSize, dryRun)
        break
      case 'product_review':
      case 'product-review':
      case 'reviews':
        await this.syncProductReviews(batchSize, dryRun)
        break
      default:
        this.logger.error(`❌ Unknown resource type: ${resourceType}`)
        this.logger.info('Available types: post, stream, product_review, all')
        return
    }
  }

  private async syncPosts(batchSize: number, dryRun: boolean) {
    this.logger.info('📝 Syncing Post likes...')
    
    // Get all unique post IDs that have likes
    const likedPostIds = await db
      .from(ZnUserLikeResource.table)
      .select('resourceId')
      .where('resourceType', RESOURCE_TYPE.POST)
      .whereNull('deletedAt')
      .groupBy('resourceId')
      .pluck('resourceId')

    this.logger.info(`Found ${likedPostIds.length} posts with likes`)

    // Also get posts that have interaction records but might be out of sync
    const interactionPostIds = await db
      .from(ZnResourceInteracts.table)
      .select('resourceId')
      .where('resource', this.trackingService.getResourceByAction(TRACKING_ACTION.ADD_WISHLIST))
      .where('likeCount', '>', 0)
      .pluck('resourceId')

    // Combine and deduplicate
    const allPostIds = [...new Set([...likedPostIds, ...interactionPostIds])]
    this.logger.info(`Total ${allPostIds.length} posts to sync`)

    await this.processBatch('Posts', allPostIds, batchSize, dryRun)
  }

  private async syncStreams(batchSize: number, dryRun: boolean) {
    this.logger.info('🎥 Syncing Stream likes...')
    
    // Get all unique stream IDs that have likes
    const likedStreamIds = await db
      .from(ZnUserLikeResource.table)
      .select('resourceId')
      .where('resourceType', RESOURCE_TYPE.STREAM)
      .whereNull('deletedAt')
      .groupBy('resourceId')
      .pluck('resourceId')

    this.logger.info(`Found ${likedStreamIds.length} streams with likes`)

    await this.processBatch('Streams', likedStreamIds, batchSize, dryRun)
  }

  private async syncProductReviews(batchSize: number, dryRun: boolean) {
    this.logger.info('⭐ Syncing Product Review likes...')
    
    // Get all unique product review IDs that have likes
    const likedReviewIds = await db
      .from(ZnUserLikeResource.table)
      .select('resourceId')
      .where('resourceType', RESOURCE_TYPE.PRODUCT_REVIEW)
      .whereNull('deletedAt')
      .groupBy('resourceId')
      .pluck('resourceId')

    this.logger.info(`Found ${likedReviewIds.length} product reviews with likes`)

    await this.processBatch('Product Reviews', likedReviewIds, batchSize, dryRun)
  }

  private async processBatch(resourceName: string, resourceIds: string[], batchSize: number, dryRun: boolean) {
    if (resourceIds.length === 0) {
      this.logger.info(`No ${resourceName} to sync`)
      return
    }

    const totalBatches = Math.ceil(resourceIds.length / batchSize)
    
    for (let i = 0; i < totalBatches; i++) {
      const batch = resourceIds.slice(i * batchSize, (i + 1) * batchSize)
      const batchNumber = i + 1
      
      this.logger.info(`Processing ${resourceName} batch ${batchNumber}/${totalBatches} (${batch.length} items)`)
      
      if (!dryRun) {
        // Process batch in parallel but with some delay to avoid overwhelming the queue
        const promises = batch.map((resourceId, index) => 
          this.scheduleSync(resourceId, index * 100) // 100ms delay between each job
        )
        
        await Promise.all(promises)
      } else {
        this.logger.info(`Would sync ${batch.length} ${resourceName} resources: ${batch.slice(0, 5).join(', ')}${batch.length > 5 ? '...' : ''}`)
      }
      
      // Small delay between batches
      if (batchNumber < totalBatches) {
        await new Promise(resolve => setTimeout(resolve, 1000))
      }
    }
    
    this.logger.success(`✅ Queued ${resourceIds.length} ${resourceName} for sync`)
  }

  private async scheduleSync(resourceId: string, delay: number = 0): Promise<void> {
    return new Promise((resolve) => {
      setTimeout(async () => {
        try {
          await queue.dispatch(
            SyncInteractsDataJob,
            {
              resourceId,
              resource: this.trackingService.getResourceByAction(TRACKING_ACTION.ADD_WISHLIST),
              actions: [TRACKING_ACTION.ADD_WISHLIST],
            },
            { 
              queueName: 'syncData',
              delay: delay
            }
          )
          resolve()
        } catch (error) {
          this.logger.error(`Failed to queue sync for resource ${resourceId}:`, error)
          resolve() // Don't fail the entire batch
        }
      }, delay)
    })
  }

  private async cleanupOrphanedRecords(dryRun: boolean) {
    this.logger.info('🧹 Checking for orphaned interaction records...')
    
    // Find interaction records that have likeCount > 0 but no corresponding likes in zn_users_like_resources
    const orphanedRecords = await db
      .from(ZnResourceInteracts.table)
      .select('resourceId', 'resource', 'likeCount')
      .where('likeCount', '>', 0)
      .whereNotExists((query) => {
        query
          .select('*')
          .from(ZnUserLikeResource.table)
          .whereRaw('zn_users_like_resources.resourceId = zn_resource_interacts.resourceId')
          .whereNull('deletedAt')
      })

    if (orphanedRecords.length > 0) {
      this.logger.warn(`Found ${orphanedRecords.length} orphaned interaction records`)
      
      if (!dryRun) {
        // Reset these records to have 0 likes and empty users array
        for (const record of orphanedRecords) {
          await db
            .from(ZnResourceInteracts.table)
            .where('resourceId', record.resourceId)
            .update({
              likeCount: 0,
              likeUsers: JSON.stringify([])
            })
        }
        this.logger.success(`✅ Cleaned up ${orphanedRecords.length} orphaned records`)
      } else {
        this.logger.info(`Would clean up ${orphanedRecords.length} orphaned records`)
      }
    } else {
      this.logger.success('✅ No orphaned records found')
    }
  }

  // Add command flags
  static flags = {
    resource: {
      type: 'string' as const,
      description: 'Specific resource type to sync (post, stream, product_review, or all)',
      default: 'all'
    },
    batchSize: {
      type: 'number' as const,
      description: 'Number of resources to process in each batch',
      default: 100
    },
    dryRun: {
      type: 'boolean' as const,
      description: 'Run in dry-run mode (no actual changes)',
      default: false
    }
  }
}
