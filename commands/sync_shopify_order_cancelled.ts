import SyncOrderCancelledJob from '#jobs/shopify/sync_order_cancelled_job'
import { ShopifyService } from '#services/shopify/shopify_service'
import { BaseCommand } from '@adonisjs/core/ace'
import type { CommandOptions } from '@adonisjs/core/types/ace'
import queue from '@rlanz/bull-queue/services/main'

export default class SyncShopifyOrderCancelled extends BaseCommand {
  static commandName = 'shopify:sync-order-cancelled'
  static description = ''

  static options: CommandOptions = {
    startApp: true,
  }

  async run() {
    this.logger.info('Hello world from "SyncShopifyOrderCancelled"')

    const shopifyService = new ShopifyService()
    let endCursor = null
    let hasNextPage = true

    while (hasNextPage) {
      const { orders, pageInfo } = await shopifyService.fetchOrders(endCursor, 'status:cancelled')
      await queue.dispatch(SyncOrderCancelledJob, { orders }, { queueName: 'syncData' })

      // Pagination
      hasNextPage = pageInfo.hasNextPage
      endCursor = pageInfo.endCursor
    }
  }
}
