import ZnBundleCollection from '#models/zn_bundle_collection'
import ZnBundleCollectionItem from '#models/zn_bundle_collection_item'
import ZnBundleProduct from '#models/zn_bundle_product'
import ZnBundleProductDiscount from '#models/zn_bundle_product_discount'
import ZnBundleProductItem from '#models/zn_bundle_product_item'
import ZnProduct from '#models/zn_product'
import ZnProductVariant from '#models/zn_product_variant'
import {
  formatDBDate,
  getShopifyProductId,
  getShopifyVariantId,
} from '../../../services/commons.js'
import { BUNDLE_DISCOUNT_TYPE } from '../../constants/bundle_product.js'
import { FastBundleService } from '../shopify/fast_bundle_service.js'

export class SyncBundleProductService {
  private fastBundle: FastBundleService
  constructor() {
    this.fastBundle = new FastBundleService()
  }

  public async disableBundleProduct(product: ZnProduct) {
    // Disable bundle product
    const bundleProductIds = (
      await ZnBundleProductItem.query()
        .where('productId', product.id)
        .select('bundleProductId')
        .groupBy('bundleProductId')
    ).map((item) => item.bundleProductId)
    await ZnBundleProduct.query().whereIn('id', bundleProductIds).update({ isActive: false })
  }

  public async syncBundleProductItems(bundle: any, bundleProduct: ZnBundleProduct) {
    if (!bundle.items) {
      return
    }

    for (const item of bundle.items) {
      const product = await ZnProduct.findBy('shopifyProductId', getShopifyProductId(item.id))
      if (!product) {
        continue
      }

      const shopifyVariantIds = item.variants.map((variant: any) => getShopifyVariantId(variant.id))

      const bundleProductItem = await ZnBundleProductItem.updateOrCreate(
        {
          fastBundleId: item.item_id,
        },
        {
          bundleProductId: bundleProduct.id,
          fastBundleId: item.item_id,
          title: item.title,
          productId: product.id,
          quantity: item.quantity,
          ignoresDiscount: item.ignores_discount,
          showInPage: item.show_in_page,
          plusOneQuantity: item.plus_one_quantity,
          isRequired: item.is_required,
          isActive: item.status === 'active',
          price: item.raw_price,
          publishedAt: formatDBDate(item.published_at),
        }
      )

      const variantIds = (
        await ZnProductVariant.query().whereIn('shopifyVariantId', shopifyVariantIds).select('id')
      ).map((variant) => variant.id)

      await bundleProductItem.related('variants').sync(variantIds)
    }
  }

  public async syncBundleCollectionItems(bundle: any, zurnoBundle: ZnBundleProduct) {
    if (!bundle.collection_items) {
      return
    }
    const fastBundleService = new FastBundleService()

    for (const item of bundle.collection_items) {
      if (!item.collection) {
        continue
      }
      const fastBundleId = item.collection.id

      const bundleCollection = await ZnBundleCollection.updateOrCreate(
        {
          fastBundleId,
        },
        {
          bundleId: zurnoBundle.id,
          fastBundleId,
          title: item.collection.title,
          quantity: item.quantity,
          ignoresDiscount: item.ignores_discount,
          showInPage: item.show_in_page,
          plusOneQuantity: item.plus_one_quantity,
          isActive: item.is_active,
          productCount: item.collection.product_count,
          maxQuantity: item.max_quantity,
          sectionTitle: item.section_title,
          sectionDescription: item.section_description,
          image: item.collection.image,
        }
      )

      const items = (await fastBundleService.getBundleCollectionById(item.collection.id)) ?? []
      for (const item of items) {
        const variants = await ZnProductVariant.query().whereIn(
          'legacyResourceId',
          item.variants.map((variant: any) => variant.id)
        )
        const productId = variants?.[0]?.productId

        const collectionItem = await ZnBundleCollectionItem.updateOrCreate(
          {
            fastBundleId: item.id,
            bundleCollectionId: bundleCollection.id,
          },
          {
            bundleId: zurnoBundle.id,
            bundleCollectionId: bundleCollection.id,
            fastBundleId: item.id,
            title: item.title,
            image: item.image,
            productId,
          }
        )
        const variantIds = variants.map((variant) => variant.id)

        await collectionItem.related('variants').sync(variantIds)
      }

      await new Promise((resolve) => setTimeout(resolve, 3000)) // 3 sec
    }
  }

  public async syncBundleProductDiscounts(bundle: any, bundleProduct: ZnBundleProduct) {
    // const TYPE_MAP = {
    //   FIX: BUNDLE_DISCOUNT_TYPE.FIX,
    //   PER: BUNDLE_DISCOUNT_TYPE.PER,
    //   SET: BUNDLE_DISCOUNT_TYPE.SET,
    // }
    for (const discount of bundle.discounts) {
      let type = BUNDLE_DISCOUNT_TYPE.FIX
      if (discount.type === 'SET') {
        type = BUNDLE_DISCOUNT_TYPE.SET
      } else if (discount.type === 'PER') {
        type = BUNDLE_DISCOUNT_TYPE.PER
      }
      await ZnBundleProductDiscount.updateOrCreate(
        {
          fastBundleId: discount.id,
        },
        {
          bundleProductId: bundleProduct.id,
          fastBundleId: discount.id,
          type,
          value: discount.value,
          quantity: discount.quantity,
          maxQuantity: discount.max_quantity,
          description: discount.description,
          isDefault: discount.is_default,
        }
      )
    }
  }

  public async syncEachBundleProduct(bundle: any) {
    const mainProduct = bundle.bap_product_id
      ? await ZnProduct.findBy('shopifyProductId', getShopifyProductId(bundle.bap_product_id))
      : null

    const bundleProduct = await ZnBundleProduct.updateOrCreate(
      {
        fastBundleId: bundle.id,
      },
      {
        fastBundleId: bundle.id,
        fastBundleUuid: bundle.uuid,
        title: bundle.title,
        type: bundle.type,
        isActive: bundle.enabled,
        hasQuantityCap: bundle.has_quantity_cap,
        itemType: bundle.item_type,
        bapId: bundle.bap_product_id,
        mainProductId: mainProduct?.id,
      }
    )
    if (bundle.items) {
      // Sync bundle PRODUCT items
      await this.syncBundleProductItems(bundle, bundleProduct)
    }
    if (bundle.collection_items) {
      // Sync bundle COLLECTION items
      await this.syncBundleCollectionItems(bundle, bundleProduct)
    }

    // Sync bundle product discounts
    await this.syncBundleProductDiscounts(bundle, bundleProduct)
  }

  public async syncBundleProduct(product: ZnProduct) {
    // Disable bundle product
    await this.disableBundleProduct(product)

    // Get fast bundle by product
    const bundles = await this.fastBundle.getBundleByProductId(product)

    await this.syncBundleProductByBundle(bundles)
  }

  public async syncBundleProductByBundle(bundles: any[]) {
    if (bundles.length > 0) {
      for (const bundle of bundles) {
        await this.syncEachBundleProduct(bundle)
      }
    }
  }
}
