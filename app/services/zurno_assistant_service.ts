import ZnAIAssistant, { EAIAssistantRole } from '#models/zn_ai_assistant'
import OpenAI from 'openai'
import {AgentInit, AgentResponse, ChatbotAgent} from "#services/chatbot/chatbot_agent_interface";
import {loadPrompt} from "#services/chatbot/prompt_loader";
import ShoppingAgent from "#services/chatbot/shopping_agent";
import OrderAgent from "#services/chatbot/order_agent";
import CustomerServiceAgent from "#services/chatbot/recommendation/customer_service_agent";
import PostAgent from "#services/chatbot/post_agent";
import ZnChatMessage from "#models/zn_chat_message";

type AgentConstructor = new (  assistantId: string, openAIAssistantId: string) => ChatbotAgent


export interface InitialResult {
  firstMessage : AgentResponse,
  needsSecond : boolean,
  _agent : ChatbotAgent,
  nextAgent? : ChatbotAgent,
}

export default class ZurnoAssistantService {
  private openai = new OpenAI()
  private orchestratorPrompt? : string


  async makeAgent(role: EAIAssistantRole) {
    const assistant = await ZnAIAssistant.findBy({ role})

    if (!assistant) {
      console.log(`${role} not found`)
    }

    const AGENT_BY_ROLE: Record<EAIAssistantRole, AgentConstructor> = {
      [EAIAssistantRole.SHOPPING_ASSISTANT]:   ShoppingAgent,
      [EAIAssistantRole.ORDER_ASSISTANT]:      OrderAgent,
      [EAIAssistantRole.CUSTOMER_SERVICE]:     CustomerServiceAgent,
      [EAIAssistantRole.POST_ASSISTANT]: PostAgent,
      // TODO: ReceptionistService not implemented yet — replace CustomerServiceAgent with ReceptionistServiceAgent when ready.
      [EAIAssistantRole.RECEPTIONIST_SERVICE] : CustomerServiceAgent
    }

    const agentConstructor = AGENT_BY_ROLE[role]
    if (!agentConstructor) { console.error(` Failed to initiate agent for role: ${role}`) }

    return new agentConstructor(
      assistant!.id,
      assistant!.openaiAssistantId
    )
  }

  async getInitialMessage(init: AgentInit, roomId: string): Promise<InitialResult> {
    const role = await this.selectAgentToResponse(init.userMessage, roomId)
    console.log('Responding agent:', role)
    const agent = await this.makeAgent(role)

    await this.openai.beta.threads.messages.create(init.threadId, {
      role: 'user',
      content: init.userMessage
    })

    if (agent.needsPreContext()) {
      const context = await agent.buildPreContext(init)
      if (context.trim()) {
        await this.openai.beta.threads.messages.create(init.threadId, {
          role: 'assistant',
          content: context,
        })
      }
    }

    const firstMessage = await agent.getInitialResponse(init)
    const shouldContinue = agent.takeNextTurn(firstMessage)

    return {
      firstMessage: firstMessage,
      needsSecond: agent.needsPostProcess(firstMessage),
      _agent: agent,
      ...(shouldContinue ? {nextAgent : agent} : {})
    }

  }

  async getPostProcessed(init: AgentInit, agent: ChatbotAgent, firstResponse: AgentResponse) {
    return agent.postProcess(firstResponse, init)
  }

  private async selectAgentToResponse(userMessage: string, roomId: string) {
    const lastMessage = await ZnChatMessage
      .query()
      .where('roomId', roomId)
      .orderBy('createdAt', 'desc')
      .offset(1)
      .first()
    const lastMessageAttributes = lastMessage?.attributes as {nextAgent?: EAIAssistantRole} | undefined
    const nextRole = lastMessageAttributes?.nextAgent
    console.log('Assigned agent:', nextRole)
    if (nextRole && Object.values(EAIAssistantRole).includes(nextRole)) {
      return nextRole
    }
    return this.route(userMessage)
  }

  private async route(userMessage: string): Promise<EAIAssistantRole> {
    if (!this.orchestratorPrompt) {
      this.orchestratorPrompt = await loadPrompt("orchestrator")
    }

    const response = await this.openai.chat.completions.create({
      model: 'gpt-3.5-turbo',
      temperature: 0.1,
      messages: [
        {role: 'system', content: this.orchestratorPrompt},
        {role: 'user', content: userMessage},
      ]
    })

    const defaultAgent = 'CUSTOMER_SERVICE' as keyof typeof EAIAssistantRole
    const selectedAgent = JSON.parse(response?.choices?.[0]?.message?.content ?? '{}').agent ?? defaultAgent
    try {
      switch ( selectedAgent) {
        case 'SHOPPING_ASSISTANT' : return EAIAssistantRole.SHOPPING_ASSISTANT
        case 'ORDER_ASSISTANT'    : return EAIAssistantRole.ORDER_ASSISTANT
        case 'POST_ASSISTANT'     : return EAIAssistantRole.POST_ASSISTANT
        default:                    return EAIAssistantRole.CUSTOMER_SERVICE
      }
    } catch (error) {
      console.error(error)
      return EAIAssistantRole.CUSTOMER_SERVICE
    }
  }


}
