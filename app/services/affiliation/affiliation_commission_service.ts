import { EApprovalStatus } from "#constants/approval_status";
import { ACTION, RESOURCE } from "#constants/authorization";
import { NOTIFICATION_TYPE } from "#constants/notification";
import CommissionCancelNotification from "#mails/affiliation/commission_cancel_notification";
import CommissionRefundNotification from "#mails/affiliation/commission_refund_notification";
import Notification from "#models/notification";
import ZnAffiliateCommission from "#models/zn_affiliate_commission";
import ZnAffiliateCommissionDetail from "#models/zn_affiliate_commission_detail";
import ZnOrder from "#models/zn_order";
import ZnUser from "#models/zn_user";
import { NotificationService } from "#services/notification_service";
import env from "#start/env";
import logger from "@adonisjs/core/services/logger";
import mail from "@adonisjs/mail/services/main";
import { AdminNotificationService } from "../../../admin/services/notification/admin_notification_service.js";
import ZnAffiliate from "#models/zn_affiliate";

export class AffiliationCommissionService {
  private adminNotificationService: AdminNotificationService

  constructor() {
    this.adminNotificationService = new AdminNotificationService()
  }

  async getCommissionById(commissionId: string) {
    return await ZnAffiliateCommission
      .query()
      .preload('affiliate', (table) => {
        table.preload('user').withScopes((scopes) => scopes.withAll())
      })
      .preload('order', (table) => {
        table.preload('user')
      })
      .preload('refCode')
      .preload('commissionDetails', (table) => {
        table.preload('orderDetail')
      })
      .where('id', commissionId)
      .first();
  }

  async createCommission(affiliateId: string, refCodeId: string, orderId: string, commissionAmount: number) {
    if (!affiliateId || !refCodeId || !orderId) {
      throw new Error('Not enough data')
    }

    const commission = new ZnAffiliateCommission();
    commission.affiliateId = affiliateId;
    commission.orderId = orderId;
    commission.commissionAmount = commissionAmount
    commission.status = EApprovalStatus.PENDING;
    commission.refCodeId = refCodeId;
    return await commission.save();
  }

  async createCommissionDetail(commissionId: string, orderDetailId: string, commissionAmount: number, commissionRate: number) {
    if (!commissionId || !orderDetailId || !commissionAmount || !commissionRate) {
      throw new Error('Not enough data')
    }

    const commissionDetail = new ZnAffiliateCommissionDetail();
    commissionDetail.commissionId = commissionId;
    commissionDetail.orderDetailsId = orderDetailId;
    commissionDetail.commissionAmount = commissionAmount;
    commissionDetail.commissionRate = commissionRate;
    return await commissionDetail.save();
  }

  async updateCommissionOnOrderCancelled(shopifyOrderId: string) {
    logger.debug('AffiliationCommissionService.updateCommissionOnOrderCancelled() shopifyOrderId: %s', shopifyOrderId)

    const order = await ZnOrder
      .query()
      .where('shopifyId', 'like', `%${shopifyOrderId}%`)
      .preload('orderDetails', (query) => {
        query.preload('variant')
      })
      .preload('user')
      .first()
    if (!order) return

    const commission = await ZnAffiliateCommission
      .query()
      .where('orderId', order.id)
      .preload('affiliate', (query) => {
        query
          .preload('user')
          .preload('affiliateTier', (query) => {
            query.preload('commissionGroups', (query) => {
              query.preload('products')
            })
          })
      })
      .preload('commissionDetails')
      .preload('refCode')
      .first()
    if (!commission) return

    if (commission.status == EApprovalStatus.APPROVED) {
      const affiliate = commission.affiliate

      const commissionalProductIds = affiliate.affiliateTier.commissionGroups.flatMap(group => group.products.map(product => product.id));
      const commissionalOrderDetails = order.orderDetails.filter((orderDetail) => commissionalProductIds.includes(orderDetail.variant.productId))
      const itemsSold = commissionalOrderDetails.reduce((total, orderDetails) => total + orderDetails.currentQuantity, 0)
      const totalGmv = commissionalOrderDetails.reduce((total, orderDetails) => total + orderDetails.price * orderDetails.currentQuantity, 0)
      affiliate.updateData(-1, -itemsSold, -totalGmv, -commission.finalAmount)
      await affiliate.save()

      const orderCancelledTimeString = order.updatedAt.toFormat('EEE, MMM dd, yyyy, hh:mm a');
      const audienceName = `${order.user.firstName} ${order.user.lastName}`;
      const orderTotalString = `$${order.totalPrice}`;
      const commissionAmountString = `$${commission.finalAmount}`;

      if (env.get('DISABLE_AFFILIATE_NOTIFICATIONS') === 'true') {
        // const admins = await ZnAdmin.all()
        const admins = await this.adminNotificationService.getAdminsByPermissions([
          { action: ACTION.READ, resource: RESOURCE.AFFILIATION }
        ])
        admins.forEach(async (admin) => {
          await mail.send(new CommissionCancelNotification(
            affiliate.user.firstName,
            commission.id,
            orderCancelledTimeString,
            audienceName,
            orderTotalString,
            commissionAmountString,
            commission.refCode.code,
            admin.username
          ))
            .then(() => {
              logger.info(`Commission cancelled email has been sent successfully to ${admin.username}`);
            })
            .catch((error) => {
              console.error('Error when sending email', error);
            });
        });
      } else {
        await mail.send(new CommissionCancelNotification(
          affiliate.user.firstName,
          commission.id,
          orderCancelledTimeString,
          audienceName,
          orderTotalString,
          commissionAmountString,
          commission.refCode.code,
          affiliate.user.email
        ))
          .then(() => {
            logger.info(`Commission cancelled email has been sent successfully to ${affiliate.user.email}`);
          })
          .catch((error) => {
            console.error('Error when sending email', error);
          })

        await this.sendPushNotification(
          affiliate.user,
          'commissions',
          'Commission Cancelled',
          'A commission was cancelled because the order was cancelled. Check your dashboard for details.'
        );
      }
    }

    commission.status = EApprovalStatus.REJECTED

    await commission.save()
  }

  async preUpdateCommissionOnOrderRefunded(order: ZnOrder | null) {
    if (!order) return;

    logger.debug('AffiliationCommissionService.preUpdateCommissionOnOrderRefunded() order.id: %s', order.id);
    await order.load('user');

    const commission = await ZnAffiliateCommission
      // @ts-ignore
      .query({ mode: 'write' })
      .where('orderId', order.id)
      .preload('affiliate', (query) => {
        query.preload('user')
      })
      .preload('refCode')
      .preload('commissionDetails', (query) => {
        query.preload('orderDetail');
      })
      .first()
    if (!commission) return

    if (commission.status == EApprovalStatus.REJECTED) return

    const isAdjustmentRequired = commission.status == EApprovalStatus.APPROVED

    commission.status = EApprovalStatus.PENDING
    await commission.save()

    if (isAdjustmentRequired) {
      const affiliate = commission.affiliate

      let itemsSold = 0;
      let totalGmv = 0;
      for (const commissionDetail of commission.commissionDetails) {
        if (commissionDetail.commissionRate > 0) {
          itemsSold += commissionDetail.orderDetail.currentQuantity;
          totalGmv += commissionDetail.orderDetail.price * commissionDetail.orderDetail.currentQuantity;
        }
      }

      affiliate.updateData(-1, -itemsSold, -totalGmv, -commission.finalAmount)
      await affiliate.save()

      const orderTimeString = order.createdAt.toFormat('EEE, MMM dd, yyyy, hh:mm a')
      const audienceName = `${order.user.firstName} ${order.user.lastName}`
      const orderTotalString = `$${order.totalPrice}`
      const orderCommissionableTotalString = `$${totalGmv}`
      const commissionAmountString = `$${commission.finalAmount}`
      if (env.get('DISABLE_AFFILIATE_NOTIFICATIONS') === 'true') {
        // const admins = await ZnAdmin.all()
        const admins = await this.adminNotificationService.getAdminsByPermissions([
          { action: ACTION.READ, resource: RESOURCE.AFFILIATION }
        ])
        admins.forEach(async (admin) => {
          await mail.send(new CommissionRefundNotification(
            affiliate.user.firstName,
            commission.id,
            orderTimeString,
            audienceName,
            orderTotalString,
            orderCommissionableTotalString,
            commissionAmountString,
            commission.refCode.code,
            admin.username
          ))
            .then(() => {
              logger.info(`Commission refunded email has been sent successfully to ${admin.username}`);
            })
            .catch((error) => {
              console.error('Error when sending email', error);
            });
        });
      } else {
        await mail.send(new CommissionRefundNotification(
          affiliate.user.firstName,
          commission.id,
          orderTimeString,
          audienceName,
          orderTotalString,
          orderCommissionableTotalString,
          commissionAmountString,
          commission.refCode.code,
          affiliate.user.email
        ))
          .then(() => {
            logger.info(`Commission refunded email has been sent successfully to ${affiliate.user.email}`);
          })
          .catch((error) => {
            console.error('Error when sending email', error);
          })

        await this.sendPushNotification(
          affiliate.user,
          'commissions',
          'Commission Revoked',
          'A commission was revoked due to a customer refund. Check your dashboard for details.'
        )
      }
    }
  }

  async postUpdateCommissionOnOrderRefunded(shopifyOrderId: string) {
    logger.debug('AffiliationCommissionService.postUpdateCommissionOnOrderRefunded() shopifyOrderId: %s', shopifyOrderId)

    const order = await ZnOrder
      // @ts-ignore
      .query({ mode: 'write' })
      .where('shopifyId', 'like', `%${shopifyOrderId}%`)
      .preload('orderDetails', (query) => {
        query.preload('variant')
      })
      .first()
    if (!order) return

    const commission = await ZnAffiliateCommission
      // @ts-ignore
      .query({ mode: 'write' })
      .where('orderId', order.id)
      .preload('commissionDetails')
      .first()
    if (!commission) return

    let totalCommissionAmount = 0
    for (const orderDetail of order.orderDetails) {
      const commissionDetail = commission.commissionDetails.find((comDetail) => comDetail.orderDetailsId === orderDetail.id)
      if (!commissionDetail || !commissionDetail.commissionRate) continue;

      let actualPrice = orderDetail.price * orderDetail.currentQuantity - orderDetail.discount
      if (actualPrice < 0) actualPrice = 0

      const commissionAmount = actualPrice * commissionDetail.commissionRate
      commissionDetail.commissionAmount = commissionAmount
      totalCommissionAmount += commissionAmount

      await commissionDetail.save()
    }
    commission.commissionAmount = totalCommissionAmount
    await commission.save()
  }

  async syncCommission(commissionId: string) {
    const commission = await ZnAffiliateCommission.query()
      .where('id', commissionId)
      .whereHas('affiliate', (query) => {
        query.whereNull('deletedAt')
      })
      .preload('commissionDetails', (query) => {
        query.preload('orderDetail', (query) => {
          query.preload('variant');
        });
      })
      .preload('affiliate', (query) => {
        query.preload('affiliateTier', (query) => {
          query.preload('commissionGroups', (query) => {
            query.preload('products');
          });
        });
      })
      .preload('order', (query) => {
        query.preload('orderDetails', (query) => {
          query.preload('variant');
        })
      })
      .first();
    if (!commission) return;

    for (const orderDetail of commission.order.orderDetails) {
      const commissionDetail = commission.commissionDetails.find(commDtl => commDtl.orderDetail.id === orderDetail.id);

      // Ignore if the commission is already created
      if (commissionDetail) continue;

      const productId = orderDetail.variant?.productId;
      for (const group of commission.affiliate?.affiliateTier?.commissionGroups) {
        if (group.products?.map(prod => prod.id).includes(productId)) {
          // Create commission if not existed and the product is commissionable
          await ZnAffiliateCommissionDetail.create({
            commissionId: commission.id,
            orderDetailsId: orderDetail.id,
            commissionRate: 0,
            commissionAmount: 0
          });
          break;
        }
      }
    }

    await commission.load('commissionDetails', (query) => {
      query.preload('orderDetail', (query) => {
        query.preload('variant');
      });
    });

    let totalCommissionAmount = 0;
    for (const commissionDetail of commission.commissionDetails) {
      const productId = commissionDetail.orderDetail?.variant?.productId;
      let isUpdated = false;
      for (const group of commission.affiliate?.affiliateTier?.commissionGroups) {
        if (group.products?.map(prod => prod.id).includes(productId)) {
          commissionDetail.commissionRate = group.commissionRate;

          const price = commissionDetail.orderDetail.price;
          const quantity = commissionDetail.orderDetail.currentQuantity;
          const itemSubTotal = price * quantity;
          const discount = commissionDetail.orderDetail.discount;
          const itemSubTotalAfterDiscounted = itemSubTotal - discount;

          commissionDetail.commissionAmount = itemSubTotalAfterDiscounted > 0 ? itemSubTotalAfterDiscounted * commissionDetail.commissionRate : 0;
          isUpdated = true;
          break;
        }
      }
      if (isUpdated) {
        await commissionDetail.save();
      }
      totalCommissionAmount += commissionDetail.commissionAmount;
    }
    commission.commissionAmount = totalCommissionAmount;
    await commission.save();

    return commission;
  }

  async syncCommissionsByAffiliate(affiliateId: string) {
    const affiliate = await ZnAffiliate.query()
      .where('id', affiliateId)
      .preload('commissions')
      .firstOrFail();

    for (const commission of affiliate.commissions) {
      await this.syncCommission(commission.id);
    }

    await affiliate.load('commissions');
    return affiliate;
  }

  async syncAllCommissions() {
    const commissions = await ZnAffiliateCommission.all();
    for (const commission of commissions) {
      await this.syncCommission(commission.id);
    }
  }

  private async sendPushNotification(user: ZnUser, resourceId: string, title: string, description: string) {
    const notification = await Notification.create({
      type: NOTIFICATION_TYPE.AFFILIATE,
      userId: user.id,
      resourceId: resourceId,
      title: title,
      description: description,
    })

    if (notification) {
      const notificationService = new NotificationService()
      await notificationService.send([user], [notification])
    }
  }
}