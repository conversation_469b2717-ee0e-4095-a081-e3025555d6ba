import { inject } from '@adonisjs/core'
import ZnAppointment, { EAppointmentStatus } from '#models/store_service/zn_appointment'
import ZnStoreService from '#models/store_service/zn_store_service'
import ZnStorePackage from '#models/store_service/zn_store_package'
import ZnStoreTax from '#models/store_service/zn_store_tax'
import { DateTime } from 'luxon'
import { v4 as uuidv4 } from 'uuid'

@inject()
export default class AppointmentService {
  async create(data: any) {
    const {
      storeId,
      customerId,
      startTime,
      endTime,
      notes,
      taxId,
      services = [],
      packages = [],
    } = data

    const appointment = await ZnAppointment.create({
      storeId,
      customerId,
      status: EAppointmentStatus.BOOKED,
      startTime: startTime
        ? (DateTime.fromJSDate(new Date(startTime)).toUTC().toJSDate() as any)
        : undefined,
      endTime: endTime
        ? (DateTime.fromJSDate(new Date(endTime)).toUTC().toJSDate() as any)
        : undefined,
      notes,
      taxId,
      subtotal: 0,
      discount: 0,
      taxAmount: 0,
      tipAmount: 0,
      total: 0,
    })

    if (services.length > 0) {
      const storeServices = await ZnStoreService.query()
        .whereIn('id', services)
        .where('storeId', storeId)

      for (const service of storeServices) {
        await appointment.related('services').sync({
          [service.id]: {
            id: uuidv4(),
            price: service.price,
            duration: service.duration,
          },
        })
      }
    }

    if (packages.length > 0) {
      const storePackages = await ZnStorePackage.query()
        .whereIn('id', packages)
        .where('storeId', storeId)

      for (const pkg of storePackages) {
        const inputPackage = packages.find((p: any) => p.id === pkg.id)
        const packagePrice = inputPackage && inputPackage.price ? inputPackage.price : 0

        await appointment.related('packages').sync({
          [pkg.id]: {
            id: uuidv4(),
            price: packagePrice,
          },
        })
      }
    }

    await this.calculateTotals(appointment.id)

    return this.findById(appointment.id)
  }

  async findById(id: string) {
    return ZnAppointment.query()
      .where('id', id)
      .preload('store')
      .preload('customer')
      .preload('tax')
      .preload('services')
      .preload('packages')
      .firstOrFail()
  }

  async getByStore(storeId: string, params: any = {}) {
    const { page = 1, limit = 10, status, startDate, endDate, customerId } = params

    const query = ZnAppointment.query()
      .where('storeId', storeId)
      .preload('customer')
      .preload('services')
      .preload('packages')

    if (status) {
      query.where('status', status)
    }

    if (startDate && endDate) {
      query
        .where(
          'startTime',
          '>=',
          startDate
            ? (DateTime.fromJSDate(new Date(startDate)).toUTC().startOf('day').toJSDate() as any)
            : undefined
        )
        .where(
          'endTime',
          '<=',
          endDate
            ? (DateTime.fromJSDate(new Date(endDate)).toUTC().endOf('day').toJSDate() as any)
            : undefined
        )
    }

    if (customerId) {
      query.where('customerId', customerId)
    }

    return query.orderBy('startTime', 'desc').paginate(page, limit)
  }

  async getByCustomer(customerId: string, params: any = {}) {
    const { page = 1, limit = 10, status, storeId } = params

    const query = ZnAppointment.query()
      .where('customerId', customerId)
      .preload('store')
      .preload('services')
      .preload('packages')

    if (status) {
      query.where('status', status)
    }

    if (storeId) {
      query.where('storeId', storeId)
    }

    return query.orderBy('startTime', 'desc').paginate(page, limit)
  }

  async update(id: string, data: any) {
    const appointment = await this.findById(id)
    const { storeId, customerId, status, startTime, endTime, notes, taxId, services, packages } =
      data

    if (storeId) appointment.storeId = storeId
    if (customerId) appointment.customerId = customerId
    if (status) appointment.status = status
    if (startTime)
      appointment.startTime = startTime
        ? (DateTime.fromJSDate(new Date(startTime)).toUTC().toJSDate() as any)
        : undefined
    if (endTime)
      appointment.endTime = endTime
        ? (DateTime.fromJSDate(new Date(endTime)).toUTC().toJSDate() as any)
        : undefined
    if (notes !== undefined) appointment.notes = notes
    if (taxId !== undefined) appointment.taxId = taxId

    await appointment.save()

    if (services) {
      if (services.length > 0) {
        const storeServices = await ZnStoreService.query()
          .whereIn('id', services)
          .where('storeId', appointment.storeId)

        for (const service of storeServices) {
          await appointment.related('services').sync({
            [service.id]: {
              id: uuidv4(),
              price: service.price,
              duration: service.duration,
            },
          })
        }
      }
    }

    if (packages) {
      if (packages.length > 0) {
        const storePackages = await ZnStorePackage.query()
          .whereIn('id', packages)
          .where('storeId', appointment.storeId)

        for (const pkg of storePackages) {
          const inputPackage = packages.find((p: any) => p.id === pkg.id)
          const packagePrice = inputPackage && inputPackage.price ? inputPackage.price : 0

          await appointment.related('packages').sync({
            [pkg.id]: {
              id: uuidv4(),
              price: packagePrice,
            },
          })
        }
      }
    }

    if (services || packages || taxId !== undefined) {
      await this.calculateTotals(id)
    }

    return this.findById(id)
  }

  async updateStatus(id: string, status: EAppointmentStatus) {
    const appointment = await this.findById(id)
    appointment.status = status
    await appointment.save()
    return appointment
  }

  async delete(id: string) {
    const appointment = await this.findById(id)
    await appointment.delete()
    return { success: true }
  }

  async calculateTotals(id: string) {
    const appointment = await this.findById(id)
    let subtotal = 0

    const services = await appointment.related('services').query()
    for (const service of services) {
      const servicePrice =
        typeof service.$extras.pivot_price === 'number'
          ? service.$extras.pivot_price
          : parseFloat(service.$extras.pivot_price || '0')
      subtotal += servicePrice || 0
    }

    const packages = await appointment.related('packages').query()
    for (const pkg of packages) {
      const packagePrice =
        typeof pkg.$extras.pivot_price === 'number'
          ? pkg.$extras.pivot_price
          : parseFloat(pkg.$extras.pivot_price || '0')
      subtotal += packagePrice || 0
    }

    let taxAmount = 0
    if (appointment.taxId) {
      const tax = await ZnStoreTax.find(appointment.taxId as string)
      if (tax) {
        taxAmount = (subtotal * tax.value) / 100
      }
    }

    const roundedSubtotal = parseFloat(subtotal.toFixed(2))
    const roundedTaxAmount = parseFloat(taxAmount.toFixed(2))
    const roundedTotal = parseFloat((roundedSubtotal + roundedTaxAmount).toFixed(2))

    appointment.subtotal = roundedSubtotal
    appointment.taxAmount = roundedTaxAmount
    appointment.total = roundedTotal

    await appointment.save()
    return appointment
  }
}
