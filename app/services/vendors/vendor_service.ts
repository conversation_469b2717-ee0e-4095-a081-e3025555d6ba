import ZnUser from "#models/zn_user";
import ZnVendor from "#models/zn_vendor";
import env from "#start/env";
import { md5 } from 'js-md5'

export default class VendorService {
  async getSettings() {
    const defaultCommissionRate = env.get('VENDORS_SETTINGS_DEFAULT_COMMISSION_RATE');
    const defaultFixedCommissionAmount = env.get('VENDORS_SETTINGS_DEFAULT_FIXED_COMMISSION_AMOUNT');

    return {
      defaultCommissionRate: defaultCommissionRate ? parseFloat(defaultCommissionRate) : 0,
      defaultFixedCommissionAmount: defaultFixedCommissionAmount ? parseFloat(defaultFixedCommissionAmount) : 0
    };
  }

  async getAllVendors(page: number, limit: number, search: string | undefined, filter?: string[]) {
    const query = ZnVendor.query();

    if (search) {
      query.whereILike('companyName', `%${search}%`)
        .orWhereILike('brandName', `%${search}%`)
        .orWhereILike('website', `%${search}%`)
        .orWhereILike('contactName', `%${search}%`)
        .orWhereILike('phone', `%${search}%`)
        .orWhereILike('email', `%${search}%`);
    }

    if (filter) {
      query.where((queryBuilder) => {
        filter.map((fil: string) =>
          queryBuilder.orWhereRaw(`LOWER(${fil.split('=')[0]}) LIKE LOWER(?)`, [
            `%${fil.split('=')[1]}%`,
          ])
        )
      })
    }

    query.orderBy('companyName', 'asc');

    return await query.paginate(page, limit);
  }

  async getVendorById(vendorId: string) {
    return await ZnVendor.query()
      .where('id', vendorId)
      .preload('businessLicenseDocuments')
      .preload('users')
      .firstOrFail();
  }

  async getVendorByUserId(userId: string) {
    return await ZnVendor.query()
      .whereHas('users', (query) => {
        query.where('id', userId);
      })
      .firstOrFail();
  }

  async createVendor(newVendor: any) {
    // Verify whether the vendor existed
    let existingVendor = await ZnVendor.query()
      .where('companyName', newVendor.companyName)
      .first();
    if (existingVendor) {
      return {
        success: false,
        messagge: 'This company name is already registered'
      }
    }

    existingVendor = await ZnVendor.query()
      .where('ein', newVendor.ein)
      .first();
    if (existingVendor) {
      return {
        success: false,
        messagge: 'This EIN is already registered'
      }
    }

    existingVendor = await ZnVendor.query()
      .where('email', newVendor.email)
      .first();
    if (existingVendor) {
      return {
        success: false,
        messagge: 'This email is already used by an existing vendor'
      }
    }

    // Create vendor
    const createdVendor = await ZnVendor.create({
      companyName: newVendor.companyName,
      brandName: newVendor.brandName,
      website: newVendor.website,
      contactName: newVendor.contactName,
      phone: newVendor.phone,
      email: newVendor.email,
      address1: newVendor.address1,
      address2: newVendor.address2,
      city: newVendor.city,
      state: newVendor.state,
      country: newVendor.country,
      zipCode: newVendor.zipCode,
      ein: newVendor.ein,
    });
    await createdVendor.related('businessLicenseDocuments').sync(newVendor.businessLicenseDocumentIds);

    // Create user
    await this.createUserForVendor(createdVendor.id, newVendor.contactName, newVendor.phone, newVendor.email);

    return {
      success: true,
      vendor: createdVendor
    };
  }

  async update(vendorId: string, payload: any) {
    const updatingVendor = await ZnVendor.findOrFail(vendorId);

    const updatableFields = [
      'companyName',
      'brandName',
      'website',
      'contactName',
      'phone',
      'email',
      'address1',
      'address2',
      'city',
      'state',
      'country',
      'zipCode',
      'ein',
      'registrationStatus',
      'rejectionReason',
      'commissionRate',
      'fixedCommissionAmount'
    ];

    updatableFields.forEach(field => {
      if (Object.prototype.hasOwnProperty.call(payload, field)) {
        (updatingVendor as any)[field] = payload[field];
      }
    });
    await updatingVendor.save();

    if (payload.businessLicenseDocumentIds) {
      await updatingVendor.related('businessLicenseDocuments').sync(payload.businessLicenseDocumentIds);
      await updatingVendor.load('businessLicenseDocuments');
    }

    // Create new user if necessary
    if (payload.email && payload.email !== updatingVendor.email) {
      await this.createUserForVendor(updatingVendor.id, payload.contactName, payload.phone, payload.email);
    }

    return updatingVendor;
  }

  async delete(vendorId: string) {
    const vendor = await ZnVendor.find(vendorId);
    if (!vendor) return;
    await vendor.softDelete();
  }

  private async createUserForVendor(vendorId: string, contactName: string, phone: string, email: string) {
    const nameParts = this.splitContactName(contactName);
    const timestamp = Date.now().toString();

    const user = await ZnUser.firstOrCreate({
      email
    }, {
      firstName: nameParts.firstName,
      lastName: nameParts.lastName,
      phone,
      email,
      password: md5(timestamp)
    });

    user.vendorId = vendorId;
    return await user.save();
  }

  private splitContactName(contactName: string): { firstName: string; lastName: string; } {
    // Handle empty or whitespace-only strings
    if (!contactName || !contactName.trim()) {
      return { firstName: '', lastName: '' };
    }

    const trimmed = contactName.trim();
    const parts = trimmed.split(/\s+/); // Split on one or more whitespace characters

    if (parts.length === 1) {
      // Only one name provided
      return { firstName: parts[0], lastName: '' };
    }

    const firstName = parts[0];
    const lastName = parts.slice(1).join(' ');

    return { firstName, lastName };
  }
}