import { fastBundleConfig } from '#config/shopify'
import ZnBundleProduct from '#models/zn_bundle_product'
import ZnBundleProductDiscount from '#models/zn_bundle_product_discount'
import ZnBundleProductItem from '#models/zn_bundle_product_item'
import ZnProduct from '#models/zn_product'
import ZnProductVariant from '#models/zn_product_variant'
import axios, { AxiosInstance } from 'axios'
import {
  formatDBDate,
  getIdFromShopifyId,
  getShopifyProductId,
  getShopifyVariantId,
} from '../../../services/commons.js'
import { BUNDLE_DISCOUNTS, BUNDLE_DISCOUNT_TYPE } from '../../constants/bundle_product.js'

export class FastBundleService {
  private fastBundleAPI: AxiosInstance
  constructor() {
    this.fastBundleAPI = axios.create({
      baseURL: fastBundleConfig.url,
      headers: {
        'Cache-Control': 'no-cache',
        'Content-Type': 'application/json',
      },
    })
  }

  public async getBundleByProductId(product: ZnProduct) {
    const productId = getIdFromShopifyId(product.shopifyProductId)
    const response = await this.fastBundleAPI.get(
      `product-bundles?product_id=${productId}&shop=${fastBundleConfig.shop}`
    )
    return response.data
  }

  public async getBundleByBundleProductId(productId: string) {
    try {
      const response = await this.fastBundleAPI.get(
        `baps/${productId}?shop=${fastBundleConfig.shop}`
      )
      return response?.data
    } catch (error) {
      console.log(error)
      return null
    }
  }

  public async getBundleCollectionById(id: number) {
    try {
      const response = await this.fastBundleAPI.get(
        `collection-products?shop_domain=${fastBundleConfig.shop}&collection_id=${id}&page=1&lang=en&limit=100`
      )
      return response?.data?.results
    } catch (error) {
      console.log(error)
      return null
    }
  }

  public async syncBundleProduct(product: ZnProduct) {
    // Disable bundle product
    const bundleProductIds = (
      await ZnBundleProductItem.query()
        .where('productId', product.id)
        .select('bundleProductId')
        .groupBy('bundleProductId')
    ).map((item) => item.bundleProductId)
    await ZnBundleProduct.query().whereIn('id', bundleProductIds).update({ isActive: false })

    // Sync bundle product
    const bundles = await this.getBundleByProductId(product)
    if (bundles.length > 0) {
      for (const bundle of bundles) {
        const bundleProduct = await ZnBundleProduct.updateOrCreate(
          {
            fastBundleId: bundle.id,
          },
          {
            fastBundleId: bundle.id,
            fastBundleUuid: bundle.uuid,
            title: bundle.title,
            type: bundle.type,
            isActive: bundle.enabled,
            hasQuantityCap: bundle.has_quantity_cap,
          }
        )
        for (const item of bundle.items) {
          const product = await ZnProduct.findBy('shopifyProductId', getShopifyProductId(item.id))
          if (!product) {
            continue
          }

          const shopifyVariantIds = item.variants.map((variant: any) =>
            getShopifyVariantId(variant.id)
          )

          const bundleProductItem = await ZnBundleProductItem.updateOrCreate(
            {
              fastBundleId: item.item_id,
            },
            {
              bundleProductId: bundleProduct.id,
              fastBundleId: item.item_id,
              title: item.title,
              productId: product.id,
              quantity: item.quantity,
              ignoresDiscount: item.ignores_discount,
              showInPage: item.show_in_page,
              plusOneQuantity: item.plus_one_quantity,
              isRequired: item.is_required,
              isActive: item.status === 'active',
              price: item.raw_price,
              publishedAt: formatDBDate(item.published_at),
            }
          )

          const variantIds = (
            await ZnProductVariant.query()
              .whereIn('shopifyVariantId', shopifyVariantIds)
              .select('id')
          ).map((variant) => variant.id)

          await bundleProductItem.related('variants').sync(variantIds)
        }

        for (const discount of bundle.discounts) {
          await ZnBundleProductDiscount.updateOrCreate(
            {
              fastBundleId: discount.id,
            },
            {
              bundleProductId: bundleProduct.id,
              fastBundleId: discount.id,
              type:
                discount.type === BUNDLE_DISCOUNTS.PER
                  ? BUNDLE_DISCOUNT_TYPE.PER
                  : BUNDLE_DISCOUNT_TYPE.FIX,
              value: discount.value,
              quantity: discount.quantity,
              maxQuantity: discount.max_quantity,
              description: discount.description,
              isDefault: discount.is_default,
            }
          )
        }
      }
    }
  }
}
