import twilio from "twilio";
import OpenAI from "openai";
import fs from "node:fs";
import axios from "axios";
import ZnAIAssistant, {EAIAssistantRole} from "#models/zn_ai_assistant";
import ZnCall from "#models/zn_call";
import ZnCallMessage from "#models/zn_call_message";

export class ZurnoAIReceptionistService {
  private voiceResponse
  private openai
  private baseRoute = '/v1/receptions/'

  constructor() {
    this.voiceResponse = new twilio.twiml.VoiceResponse();
    this.openai = new OpenAI()
  }

  sayAndListen(message: any) {
    if(message) {
      this.voiceResponse.say({ voice: 'Polly.Joanna' }, message);
    }

    this.voiceResponse.record({
      timeout: 2,
      maxLength: 5,
      action: this.baseRoute + 'process-flow',
      transcribe: false,
      playBeep: true,
    });

    return this.voiceResponse.toString()
  }

  async translateVoiceToText(recordingUrl: any) {
    const filePath = './data/user_audio.wav';
    await this.waitForTwilioAudio(recordingUrl, filePath);

    // 2. Transcribe using OpenAI Whisper
    const transcription = await this.openai.audio.transcriptions.create({
      file: fs.createReadStream(filePath),
      model: 'whisper-1',
    });

    return transcription.text
  }

  redialToAgent() {
    this.voiceResponse.say({ voice: 'Polly.Joanna' },"Connecting you to a Zurno representative.");
    this.voiceResponse.dial(process.env.SUPPORT_PHONE_NUMBER); // agent phone or Twilio SIP endpoint
    return this.voiceResponse.toString()
  }

  handleMakeAppointment() {
    this.voiceResponse.say({ voice: 'Polly.Joanna' },"Making appointment.");
    return this.voiceResponse.toString()
  }

  async handleReception(room: ZnCall, message:ZnCallMessage, userSaid: string) {
    const assistant = await ZnAIAssistant.findBy({role: EAIAssistantRole.CUSTOMER_SERVICE})
    let answer = 'I dont have answer'
    if (assistant) {
      console.log(room, message, userSaid)
      // // const thread = await this.openai.beta.threads.create()
      // const parsed = await this.zurnoAssistanceService.getAIAnswer(assistant, room.threadId, userSaid)
      // answer = parsed?.text || 'No answer'
      //
      // //Store new message
      // message.reply = answer
      // await message.save()
    }
    console.log('AI answer:', answer);
  }

  handleLoop() {
    this.voiceResponse.redirect(this.baseRoute + 'process-flow');
    return this.voiceResponse.toString()
  }

  handleBye() {
    this.voiceResponse.say({ voice: 'Polly.Joanna' },"Bye. Have a great day!");
    return this.voiceResponse.toString()
  }

  handlePause(userMessage: ZnCallMessage) {
    this.voiceResponse.say({ voice: 'Polly.Joanna' },"Please hold while we process your request.");
    this.voiceResponse.pause({ length: 5 });
    this.voiceResponse.redirect(this.baseRoute + 'process-reception/' + userMessage.id);
    return this.voiceResponse.toString()
  }
  async waitForTwilioAudio(url: string, savePath: fs.PathLike, maxAttempts = 5, delayMs = 1500) {
    for (let attempt = 1; attempt <= maxAttempts; attempt++) {
      try {
        const response = await axios.get(url, { responseType: 'stream' });

        await new Promise((resolve, reject) => {
          const writer = fs.createWriteStream(savePath);
          response.data.pipe(writer);
          // @ts-ignore
          writer.on('finish', resolve);
          writer.on('error', reject);
        });

        return true; // success
      } catch (err) {
        if (attempt === maxAttempts) throw new Error(`Failed to download Twilio file after ${maxAttempts} attempts`);
        if (err.response?.status === 404) {
          console.log(`Recording not ready (attempt ${attempt}), retrying in ${delayMs}ms...`);
          await new Promise(res => setTimeout(res, delayMs));
        } else {
          throw err;
        }
      }
    }
  }
}
