import OpenAI from "openai";
import {Pinecone, RecordMetadata} from "@pinecone-database/pinecone";

export interface QueryResult {
  id: string,
  score: number,
  metadata?: RecordMetadata
}

export abstract class PineconeEmbeddingService<T> {
  protected readonly openai = new OpenAI()
  protected readonly pinecone = new Pinecone()

  protected constructor(
   protected readonly indexName : string,
   protected readonly embedidngModel : string,
   protected readonly dimension : number,
   protected readonly batch = 100
  ) {}

  protected abstract getId (item: T) : string
  protected abstract buildText (item: T): string
  protected abstract buildMetadata (item: T): RecordMetadata

  private async embed(texts: string[]): Promise<number[][]> {
    const { data } = await this.openai.embeddings.create({
      model : this.embedidngModel,
      input : texts,
    })

    return data.map(d => d.embedding)
  }

  private async ensureIndex() {
    const { indexes } = await this.pinecone.listIndexes()
    if (!indexes?.some(index => index.name === this.indexName)) {
      const defaultSpec = {
        name : this.indexName,
        dimension: this.dimension,
        metric : 'cosine',
        spec : {serverless : {cloud: 'aws', region: 'us-east-1'}},
      } as const
      await this.pinecone.createIndex(defaultSpec)

      await new Promise(resolve => setTimeout(resolve, 1000))
    }

    return this.pinecone.index(this.indexName)
  }

  async upsert(items: T[]) {
    if (!items.length) return
    const pineconeIndex = await this.ensureIndex()

    for (let index = 0; index < items.length; index += this.batch) {
      const slice = items.slice(index, index + this.batch)
      const texts = slice.map(this.buildText, this)
      const embeddings = await this.embed(texts)

      const vectors = slice.map((item, vectorIndex) => ({
        id : this.getId(item),
        values: embeddings[vectorIndex],
        metadata: this.buildMetadata(item),
      }))

      await pineconeIndex.upsert(vectors)
      const progress = ((index + slice.length)/items.length * 100).toFixed(2)
      console.log(` Upserted ${index + slice.length}/${items.length}  (${progress} %) -> ${this.indexName}`)
    }
  }

  async query(text: string, topK = 10, filter?: Record<string, unknown>) : Promise<QueryResult[]> {
    const pineconeIndex = await this.ensureIndex()
    const [queryEmbedding] = await this.embed([text])
    const { matches = []} = await pineconeIndex.query({
      vector: queryEmbedding,
      topK,
      includeMetadata: true,
      ...(filter && Object.keys(filter).length ? { filter } : {}),
    })

    return matches.map( match => ({
      id: match.id,
      score: match.score ?? 0,
      metadata: match.metadata
    }))
  }

  async deleteIndex() {
    await this.pinecone.deleteIndex(this.indexName)
  }

}
