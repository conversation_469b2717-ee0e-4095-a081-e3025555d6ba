import {BaseChatbotAgent, EMPTY_MESSAGE_HOLDER} from "#services/chatbot/base_chatbot_agent";
import {EAIAssistantRole} from "#models/zn_ai_assistant";
import {AgentInit, AgentResponse} from "#services/chatbot/chatbot_agent_interface";
import RecommendationEngine from "#services/chatbot/recommendation/recommendation_engine";

export default class PostAgent extends BaseChatbotAgent {
  readonly role = EAIAssistantRole.POST_ASSISTANT

  constructor(assistantId: string, openAIAssistantId: string) {
    super(assistantId, openAIAssistantId)
  }

  needsPostProcess(firstResponse: AgentResponse){
    if (!firstResponse || typeof firstResponse !== 'object') return false
    const responseJSON = firstResponse as any
    return !(!responseJSON?.description || responseJSON?.status === "in_conversation");
  }

  async postProcess(firstResponse: AgentResponse, _init: AgentInit): Promise<AgentResponse> {
    const description = (firstResponse as any).description
    const postRecommendation = await this.searchPost(description)
    console.log('Response from the agent', firstResponse)
    console.log('Recommendation from the agent', postRecommendation)
    return {
      text : EMPTY_MESSAGE_HOLDER,
      questions: firstResponse.questions,
      productIds: [],
      collectionIds: [],
      postIds : postRecommendation,
      assistantId : this.openAIId
    }
  }

  takeNextTurn(firstResponse: AgentResponse){
    if (!firstResponse || typeof firstResponse !== 'object') return false
    const responseJSON = firstResponse as any
    return responseJSON?.status === "in_conversation"
  }

  private async searchPost(description: string | string[]) {
    const recommendationEngine = new RecommendationEngine()
    console.log('Searching posts with', description)
    const recommendation = await recommendationEngine.run({
      descriptions: {
        post : description
      }
    })
    return recommendation.post
  }

  // TODO: Add createPost method
  // private async createPost(finalResponse: AgentResponse): Promise<AgentResponse> {
  //
  // }
  //

}
