import {BaseChatbotAgent, EMPTY_MESSAGE_HOLDER} from "#services/chatbot/base_chatbot_agent";
import {EAIAssistantRole} from "#models/zn_ai_assistant";
import {AgentInit, AgentResponse} from "#services/chatbot/chatbot_agent_interface";
import RecommendationEngine from "#services/chatbot/recommendation/recommendation_engine";

export default class ShoppingAgent extends BaseChatbotAgent {
  readonly role = EAIAssistantRole.SHOPPING_ASSISTANT

  constructor(assistantId: string, openAIAssistantId: string) {
    super(assistantId, openAIAssistantId)
  }

  needsPreContext(): boolean { return false }

  needsPostProcess(firstResponse: AgentResponse) {
    const description = (firstResponse as any).description
    if (!description) return false

    const hasProducts = Array.isArray(description.products) && description.products.length > 0
    const hasCollections =
      Array.isArray(description.collections) && description.collections.length > 0

    /* Only run post-processing when at least one list is non-empty */
    return hasProducts || hasCollections
  }

  async postProcess(firstResponse: AgentResponse, _init: AgentInit): Promise<AgentResponse> {
    const description = (firstResponse as any).description ?? {}
    const recommendationEngine = new RecommendationEngine()
    const recommendation = await recommendationEngine.run({
      descriptions :{
        product : description.products ?? [],
        collection: description.collections ?? []
      },
      constraints: {
        product: {
          price_lower: (firstResponse as any).price_lower,
          price_upper: (firstResponse as any).price_upper,
          inStockOnly: (firstResponse as any).inStock,
        },
      },
    })

    return {
      text: EMPTY_MESSAGE_HOLDER,
      productIds: recommendation.product ?? [],
      collectionIds: recommendation.collection ?? [],
      questions: firstResponse.questions ?? [],
      assistantId: this.openAIId,
    }
  }
}
