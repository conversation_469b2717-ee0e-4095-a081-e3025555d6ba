import {BaseChatbotAgent, sanitize<PERSON>sistant<PERSON>ext} from "#services/chatbot/base_chatbot_agent";
import {EAIAssistantRole} from "#models/zn_ai_assistant";
import OpenAI from "openai";
import {AgentInit, AgentResponse} from "#services/chatbot/chatbot_agent_interface";
import ZnOrder from "#models/zn_order";
import ZnAddress from "#models/zn_address";
import {loadPrompt} from "#services/chatbot/prompt_loader";

function formatAddress(address? : ZnAddress | null) {
  if (!address) return 'n/a'
  const adr2 = address.address2 ? `, ${address.address2} ` : ''
  return `${address.name}, ${address.address1}${adr2}, ${address.city}, ` +
    `${address.province ?? ''} ${address.zip ?? ''}, ${address.country}`
}

function formatMoney(amount: number ) {
  return `$${amount.toFixed(2)}`
}

function formatTrack(id?: string | null, carrier? : string | null, url?: string | null) {
  const parts : string[] = []
  if (id) parts.push(`Tracking ID: ${id}`)
  if (carrier) parts.push(`Carrier: ${carrier}`)
  if (url) parts.push(`Url: ${url}`)
  return parts.length ? `  • Tracking → ${parts.join(' | ')}` : '  • Tracking → n/a'
}

export default class OrderAgent extends BaseChatbotAgent {
  readonly role = EAIAssistantRole.ORDER_ASSISTANT

  constructor( assistantId: string, openAIId: string) {
    super(assistantId, openAIId)
    this.openai = new OpenAI()
  }

  /* -------- 1) we ALWAYS provide pre-context ---------------------- */
  needsPreContext() { return true }

  async buildPreContext(init: AgentInit): Promise<string> {
    const filters = await this.extractFilters(init.userMessage)
    const orders  = await this.buildOrderQuery(init.userId, filters)

    if (orders.length === 0) return 'The user has no orders matching the request.'

    const lines = orders.map((order) => {
      const placed  = order.createdAt.toISO()
      const ship    = formatAddress(order.shippingAddress)
      const bill    = formatAddress(order.billingAddress)
      const first   = order.firstOrderDetail
        ? `${order.firstOrderDetail.quantity} × ${order.firstOrderDetail.title} @ ${formatMoney(order.firstOrderDetail.price)}`
        : 'No line-items recorded'
      const extras  = order.orderDetails.length - (order.firstOrderDetail ? 1 : 0)
      const details = extras > 0 ? ` (+${extras} other items)` : ''

      return [
        `• ${order.name}  (ID: ${order.id})`,
        `  • Placed: ${placed}`,
        `  • Status: ${order.status}`,
        `    • Financial: ${order.financialStatus}`,
        `    • Fulfillment: ${order.fulfillmentStatus ?? 'unfulfilled'}`,
        `  • Total: ${formatMoney(order.totalPrice)}`,
        `  • Shipping → ${ship}`,
        `  • Billing  → ${bill}`,
        `  • First item: ${first}${details}`,
        formatTrack(order.trackingNumber, order.trackingCompany, order.trackingUrl),
      ].join('\n')
    })

    return ['Here are the user’s orders:', ...lines].join('\n')
  }

  private async extractFilters(content: string) {
    const orderFilterSystemPrompt = await loadPrompt("order_tag_filter")
    try {
      const response = await this.openai.chat.completions.create({
        model:  'gpt-4.1',
        temperature: 0.1,
        messages: [
          {
            role: 'system',
            content: orderFilterSystemPrompt
          },
          {role: 'user', content}
        ]
      })
      const receivedMessage = response.choices?.[0]?.message?.content || content
      return sanitizeAssistantText(receivedMessage)
    } catch (error) {
      console.log(error)
      return {}
    }

  }

  private buildOrderQuery(userId: string, filter: any) {
    const query = ZnOrder.query().where('userId', userId)
    if (filter.order_numbers?.length)      query.whereIn('name', filter.order_numbers)
    if (filter.order_status)               query.where('orderStatus', filter.order_status)
    if (filter.financial_status)           query.where('financialStatus', String(filter.financial_status))
    if (filter.fulfillment_status)         query.where('fulfillmentStatus', String(filter.fulfillment_status))
    if (filter.total_price?.min !== undefined) query.where('totalPrice', '>=', filter.total_price.min)
    if (filter.total_price?.max !== undefined) query.where('totalPrice', '<=', filter.total_price.max)
    if (filter.fulfilled_at?.from)         query.where('createdAt', '>=', filter.fulfilled_at.from)
    if (filter.fulfilled_at?.to)           query.where('createdAt', '<=', filter.fulfilled_at.to)

    return query
      .preload('shippingAddress')
      .preload('billingAddress')
      .preload('firstOrderDetail')
      .preload('orderDetails')
      .orderBy('createdAt', 'desc')
  }


  /* -------- 3) Order agent NEVER does a second phase --------------- */
  needsPostProcess() { return false }

  async postProcess(firstResponse: AgentResponse) {return firstResponse}

}
