import {<PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>} from "#services/chatbot/chatbot_agent_interface";
import {EAIAssistantRole} from "#models/zn_ai_assistant";
import OpenAI from "openai";
import {IvsChatBotService} from "#services/aws/ivschat_bot_service";

export abstract class Base<PERSON>hatbotAgent implements ChatbotAgent {

  /* ---------------- To be supplied by subclasses -------------------- */

  abstract readonly role: EAIAssistantRole

  protected constructor(
    /** Row id in your ZnAIAssistant table – needed for IVS attributes */
    protected readonly assistantId: string,
    /** OpenAI Assistant ID (i.e. “asst_…”) for this agent */
    protected readonly openAIId: string
  ) {}

  /* ---------------- Shared infrastructure --------------------------- */

  protected openai = new OpenAI()
  protected ivsChatbotService = new IvsChatBotService()

  /* ---------------- Optional hooks (override as needed) ------------- */

  needsPreContext() { return false }

  async buildPreContext(_init: AgentInit) { return ''}

  needsPostProcess(_firstResponse: AgentResponse) { return false }

  /* ---------------- Utility helpers -------------------------------- */

  /**
  * Kick off an OpenAI **run** against an existing thread and wait until
  * it finishes. Returns the assistant’s latest message, already passed
  * through `sanitizeAssistantText`.
  *
  * NOTE: This helper never appends the user message – that is handled
  *       upstream by `ZurnoAssistantService`.
  */
  protected async runAssistant(threadId: string): Promise<AgentResponse> {
    await this.openai.beta.threads.runs.createAndPoll(threadId, {
      assistant_id: this.openAIId,
    })

    await this.ivsChatbotService.waitForLastRunToComplete(threadId)

    const {data : [latest] } = await this.openai.beta.threads.messages.list(
      threadId, {limit: 1}
    )

    const textBlock = latest.content.find((content) => content.type === "text")
    const raw = textBlock?.type === 'text' ? textBlock.text.value.trim() : ''

    return sanitizeAssistantText(raw)
  }


  /* ------------ Default FIRST-TURN implementation ------------------- */
  async getInitialResponse(init: AgentInit): Promise<AgentResponse> {
    const firstResponse = await this.runAssistant(init.threadId)
    firstResponse.assistantId = this.assistantId
    return firstResponse
  }



  /* ---------------- Abstract methods every agent must implement ----- */

  abstract postProcess(first: AgentResponse, init: AgentInit): Promise<AgentResponse>

}



/** Strip markdown fences / citations and JSON-parse when possible. */
export function sanitizeAssistantText(raw: string): AgentResponse {
  let cleaned = raw
    .replace(/^```json/i, '')
    .replace(/^```/, '')
    .replace(/```$/, '')
    .replace(/\[\d+:\d+†[^\]]+\]/g, '')
    .replace(/【[^】]+】/g, '')
    .trim()

  let parsed: AgentResponse
  try {
    parsed = JSON.parse(cleaned)
  } catch {
    parsed = {
      text: cleaned,
      assistantId: '',   // concrete agent fills this later
      productIds: [],
      collectionIds: [],
      postIds: [],
      questions: [],
    }
  }

  // Normalise & tidy arrays
  parsed.productIds    = (parsed.productIds    ?? []).map((id) => id.replace(/^product_/, '').trim())
  parsed.collectionIds = (parsed.collectionIds ?? []).map((id) => id.replace(/^collection_/, '').trim())
  parsed.postIds       = (parsed.postIds       ?? []).map((id) => id.replace(/^post_/, '').trim())
  parsed.questions     = (parsed.questions     ?? []).map((q)  => q.trim())

  return parsed
}
