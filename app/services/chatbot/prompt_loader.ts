import { promises as fs } from 'node:fs'
import { join, dirname }  from 'node:path'
import { fileURLToPath }  from 'node:url'

const HERE = dirname(fileURLToPath(import.meta.url))

const PROMPT_DIR = join(HERE, 'prompts')

const cache = new Map<string, string>()

export async function loadPrompt(name: string): Promise<string> {
  if (cache.has(name)) return cache.get(name)!

  const filePath = join(PROMPT_DIR, `${name}.txt`)
  const text = await fs.readFile(filePath, 'utf8')
  cache.set(name, text)
  return text
}
