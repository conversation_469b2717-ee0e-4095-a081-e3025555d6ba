import {EAIAssistantRole} from "#models/zn_ai_assistant";

export interface AgentInit {
  userId: string
  threadId: string
  userMessage: string
}

export interface AgentResponse {
  text: string
  productIds?: string[]
  collectionIds?: string[]
  postIds?: string[]
  questions?: string[]

  assistantId: string
}

export interface ChatbotAgent {
  readonly role: EAIAssistantRole

  /** Does it need to inject “pre-context” (e.g. order history) before the 1st OpenAI call? */
  needsPreContext(): boolean

  /** Produce that context (only called if `needsPreContext()` is true). */
  buildPreContext(init: AgentInit): Promise<string>

  /** Hit OpenAI and return the FIRST raw answer. */
  getInitialResponse(init: AgentInit): Promise<AgentResponse>

  /** Should we run a **post-processing** pass (vector search, DB look-ups, …)? */
  needsPostProcess(firstResponse: AgentResponse): boolean

  /** Execute that post-processing and return the enriched final payload. */
  postProcess(
    first: AgentResponse,
    init: AgentInit,
  ) : Promise<AgentResponse>

  takeNextTurn(firstResponse: AgentResponse): boolean

}
