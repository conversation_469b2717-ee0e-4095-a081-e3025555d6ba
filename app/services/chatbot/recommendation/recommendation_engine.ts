import ProductRecommender from "#services/chatbot/recommendation/product_recommender";
import CollectionRecommender from "#services/chatbot/recommendation/collection_recommender";
import RecommendationFitEvaluator from "#services/chatbot/recommendation/recommendation_fit_evaluator";

/* ---------- PUBLIC API ------------------------------------------------ */

export interface RecommendationEngineInput {
  productDescriptions?:    string | string[]
  collectionDescriptions?: string | string[]
  constraints?: {
    price_lower?: number
    price_upper?: number
    inStockOnly?: number
  }
}

export interface RecommendationOutput {
  productIds:    string[]
  collectionIds: string[]
}

/* ---------- INTERNAL CONSTANTS & HELPERS ------------------------------ */

const MAX_TOTAL = 5

const toArray = (x?: string | string[]) =>
  !x ? [] : Array.isArray(x) ? x : [x]

const quota = (nDesc: number) =>
  Math.max(Math.ceil(MAX_TOTAL / Math.max(1, nDesc)), 1)


export default class RecommendationEngine {
  private product     = new ProductRecommender()
  private collection  = new CollectionRecommender()
  private evaluator   = new RecommendationFitEvaluator()

  /* ---- Generic suggestions gatherer ------------------------------------------- */
  private async gatherSuggestionsIds(
    descriptions: string[],
    perDescQuota: number,
    maxTotal: number,
    query: (sentence: string) => Promise<{ id: string; score: number; metadata: any }[]>,
    fit?: (sentence: string, meta: any) => Promise<boolean>
  ): Promise<string[]> {

    const chosen: string[] = []

    for (const description of descriptions) {
      if (chosen.length >= maxTotal) break
      const matches = await query(description)
      let added = 0

      for (const match of matches) {
        if (chosen.includes(match.id)) continue
        if (fit && !(await fit(description, match.metadata))) continue

        chosen.push(match.id)
        added++
        if (added >= perDescQuota || chosen.length >= maxTotal) break
      }
    }
    return chosen
  }

  /* ---- Public entry point ----------------------------------------- */
  async run(input: RecommendationEngineInput): Promise<RecommendationOutput> {
    console.log(" ----- RUNNING RECOMMENDATION -----")
    /* -------- PRODUCTS -------- */
    const productDescriptions   = toArray(input.productDescriptions)
    console.log("Product Descriptions: ", productDescriptions)
    const productIds     = productDescriptions.length
      ? await this.gatherSuggestionsIds(
        productDescriptions,
        quota(productDescriptions.length),
        MAX_TOTAL,
        (suggestion) => this.product.query(suggestion, input.constraints ?? {}, 3),
        (suggestion, meta) => this.evaluator.isFit(suggestion, meta.description) // fit-check ON
      )
      : []

    /* -------- COLLECTIONS ------ */
    const collectionDescriptions   = toArray(input.collectionDescriptions)
    console.log("Collection Descriptions: ", collectionDescriptions)
    const colIds     = collectionDescriptions.length
      ? await this.gatherSuggestionsIds(
        collectionDescriptions,
        quota(collectionDescriptions.length),
        MAX_TOTAL,
        (suggestion) => this.collection.query(suggestion, 3),
        async () => true
      )
      : []

    return { productIds: productIds, collectionIds: colIds }
  }
}
