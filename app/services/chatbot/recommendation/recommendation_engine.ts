import ProductRecommender from "#services/chatbot/recommendation/product_recommender";
import CollectionRecommender from "#services/chatbot/recommendation/collection_recommender";
import RecommendationFitEvaluator from "#services/chatbot/recommendation/recommendation_fit_evaluator";
import PostRecommeder from "#services/chatbot/recommendation/post_recommeder";

export interface VectorDatabaseMatch {
  id: string
  score: number
  metadata: Record<string, any>
}

export interface EmbeddingService {
  query(text: string, k: number, filter?: any): Promise<any[]>
}

export interface Recommender {
  query(sentence: string, k: number, constraints?: any): Promise<VectorDatabaseMatch[]>

  evaluate?(sentence: string, metadata: any): Promise<boolean>                // default = always true
}

export type RecommendationKind = 'product' | 'collection' | 'post'

const fitEvaluator = new RecommendationFitEvaluator()


const RECOMMENDER_REGISTRY: Record<RecommendationKind, Recommender> = {
  product: {
    query: (description, k, constraints) => new ProductRecommender().query(description,k, constraints ?? {}),
    evaluate :  (description: string, metadata: { description: string; })    => fitEvaluator.isFit(description, metadata.description),
  },
  collection: {
    query: (description, k)    => new CollectionRecommender().query(description, k),
    evaluate :  async ()  => true,
  },
  post : {
    query: (description, k) => new PostRecommeder().query(description, k),
    evaluate :  async ()  => true,
  }
}
/* ---------- PUBLIC API ------------------------------------------------ */

export interface RecommendationEngineInput {
  descriptions: Partial<Record<RecommendationKind, string | string[]>>
  constraints?: Partial<Record<RecommendationKind, Record<string, any>>>
  maxTotal?: number
}


export type RecommendationEngineOutput = Record<RecommendationKind, string[]>

/* ---------- INTERNAL CONSTANTS & HELPERS ------------------------------ */

const DEFAULT_MAX_TOTAL = 5
const toArray = (x?: string | string[]) =>
  !x ? [] : Array.isArray(x) ? x : [x]

const quota = (numberDescriptions: number, maxTotal: number) =>
  Math.max(Math.ceil(maxTotal / Math.max(1, numberDescriptions)), 1)


export default class RecommendationEngine {

  /* ---- Generic suggestions gatherer ------------------------------------------- */
  private async gatherSuggestionsIds(
    descriptions: string[],
    perDescQuota: number,
    maxTotal: number,
    recommender: Recommender,
    constraints?: any
  ): Promise<string[]> {

    const chosen: string[] = []

    for (const description of descriptions) {
      if (chosen.length >= maxTotal) break
      const matches = await recommender.query(description, perDescQuota + 1, constraints)
      let added = 0

      for (const match of matches) {
        console.log('Potential match: ', match)
        if (chosen.includes(match.id)) continue
        if (recommender.evaluate && !(await recommender.evaluate(description, match.metadata))) continue
        chosen.push(match.id)
        added++
        if (added >= perDescQuota || chosen.length >= maxTotal) break
      }
    }
    return chosen
  }

  /* ---- Public entry point ----------------------------------------- */
  async run(input: RecommendationEngineInput): Promise<RecommendationEngineOutput> {
    console.log(" ----- RUNNING RECOMMENDATION -----")
    const maxTotal = input.maxTotal ?? DEFAULT_MAX_TOTAL
    const output : RecommendationEngineOutput = Object.create(null)

    for (const [kind, recommender] of Object.entries(RECOMMENDER_REGISTRY) as [RecommendationKind, Recommender][]) {
      const descriptions = toArray(input.descriptions?.[kind])
      console.log('Searching for ' ,kind , ' with descriptions: ', descriptions)
      if (descriptions.length < 1) continue
      output[kind] = await this.gatherSuggestionsIds(
        descriptions,
        quota(descriptions.length, maxTotal),
        maxTotal,
        recommender,
        input.constraints?.[kind],
      )
    }

    return output
  }

}
