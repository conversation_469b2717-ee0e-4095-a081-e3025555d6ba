import { EAIAssistantRole }    from '#models/zn_ai_assistant'
import {Agent<PERSON><PERSON><PERSON>, AgentResponse} from '#services/chatbot/chatbot_agent_interface'
import { BaseChatbotAgent }    from '#services/chatbot/base_chatbot_agent'

/**
 *  Customer-Service Agent
 *  ──────────────────────
 *  • <PERSON>les site-feedback, policy questions, chat issues, etc.
 *  • No database look-ups (so: no pre-context, no post-process).
 *  • Relies entirely on its OpenAI Assistant for answers.
 */


export default class CustomerServiceAgent extends BaseChatbotAgent {
  readonly role = EAIAssistantRole.CUSTOMER_SERVICE

  constructor( assistantId: string, openAIId: string ) {
    super(assistantId, openAIId)
  }

  /* ------------ hooks --------------------------------------------- */
  needsPreContext(): boolean { return false }

  needsPostProcess(_first: AgentResponse): boolean { return false }

  async postProcess(firstMessage: AgentResponse, _init: AgentInit): Promise<AgentResponse> {
    return firstMessage
  }
}
