import OpenAI from "openai"


export default class RecommendationFitEvaluator {
  private openai = new OpenAI()

  /**
   * Very cheap check: returns `true` if GPT replies "Yes" to the match question.
   */

  async isFit(userSentence: string, productDescription: string): Promise<boolean> {
    const prompt = `
      You are a product‐matching evaluator.
      Given the user’s request:
        "${userSentence}"
      And the product description:
        "${productDescription}"
      Does the product satisfy the request?
      Answer strictly “Yes” or “No” (no extra text).
    `.trim()

    const response = await this.openai.chat.completions.create({
      model: 'gpt-4o-mini',    // or whatever tier you prefer
      temperature: 0.0,
      messages: [
        { role: 'system', content: 'You are an evaluator.' },
        { role: 'user', content: prompt },
      ],
    })

    return response.choices[0].message.content?.trim() === 'Yes'
  }

}
