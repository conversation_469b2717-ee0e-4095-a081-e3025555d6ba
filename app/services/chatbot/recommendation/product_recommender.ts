import ProductEmbeddingService from '#services/pinecone/product_embedding_service'
import {BaseRecommender} from "#services/chatbot/recommendation/base_recommender";
import {VectorDatabaseMatch} from "#services/chatbot/recommendation/recommendation_engine";

interface ConstraintOpts {
  price_lower?: number
  price_upper?: number
  inStockOnly?: number
}



export default class ProductRecommender extends BaseRecommender{
  protected embeddingService = new ProductEmbeddingService()

  /** Compose a Pinecone filter object from constraints */
  private buildFilter({
                        price_lower,
                        price_upper,
                        inStockOnly,
                      }: {
    price_lower?: number
    price_upper?: number
    inStockOnly?: number
  }): Record<string, any> | undefined {
    const filter: Record<string, any> = {}
    if (price_lower !== undefined || price_upper !== undefined) {
      filter.price = {}
      if (price_lower !== undefined) filter.price.$gte = price_lower
      if (price_upper !== undefined) filter.price.$lte = price_upper
    }
    if (inStockOnly === 1) filter.inStock = true
    return Object.keys(filter).length ? filter : undefined
  }

  /**
   * Query Pinecone and return at most **5** items sorted by similarity.
   */
  async query(
    sentence: string,
    k = 5,
    constraints: ConstraintOpts = {},
  ): Promise<VectorDatabaseMatch[]> {
    const filter = this.buildFilter(constraints)
    return super.query(sentence, k, filter)
  }
}
