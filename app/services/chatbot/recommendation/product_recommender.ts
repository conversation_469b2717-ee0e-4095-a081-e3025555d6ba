import ProductEmbeddingService from '#services/pinecone/product_embedding_service'

export interface PineconeMatch {
  id: string
  score: number
  metadata: Record<string, any>
  userDescription: string
}

export default class ProductRecommender {
  private embeddingService = new ProductEmbeddingService()

  /** Compose a Pinecone filter object from constraints */
  private buildFilter({
                        price_lower,
                        price_upper,
                        inStockOnly,
                      }: {
    price_lower?: number
    price_upper?: number
    inStockOnly?: number
  }): Record<string, any> | undefined {
    const filter: Record<string, any> = {}
    if (price_lower !== undefined || price_upper !== undefined) {
      filter.price = {}
      if (price_lower !== undefined) filter.price.$gte = price_lower
      if (price_upper !== undefined) filter.price.$lte = price_upper
    }
    if (inStockOnly === 1) filter.inStock = true
    return Object.keys(filter).length ? filter : undefined
  }

  /**
   * Query Pinecone and return at most **5** items sorted by similarity.
   */
  async query(
    description: string,
    constraints: {
      price_lower?: number
      price_upper?: number
      inStockOnly?: number
    },
    k = 5
  ): Promise<PineconeMatch[]> {
    const filter = this.buildFilter(constraints)
    const matches = await this.embeddingService.query(description, k, filter)
    return matches.map((match) => ({
      id: match.id || '',
      score: match.score,
      metadata: match.metadata ?? {},
      userDescription: description,
    }))
  }
}
