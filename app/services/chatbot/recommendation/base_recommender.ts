import {EmbeddingService, VectorDatabaseMatch, Recommender} from "#services/chatbot/recommendation/recommendation_engine";

export abstract class BaseRecommender implements Recommender {
  protected abstract embeddingService :  EmbeddingService

  async query(sentence: string, k=3, constraints?: any) : Promise<VectorDatabaseMatch[]> {
    const rows = await this.embeddingService.query(sentence, k, constraints)
    return rows.map(result => ({
      id: result.id,
      score: result.score,
      metadata: result.metadata ?? {}
    }))
  }

  async evaluate() { return true }
}
