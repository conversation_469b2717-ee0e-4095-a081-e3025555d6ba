import CollectionEmbeddingService from "#services/pinecone/collection_embedding_service";


export default class CollectionRecommender {
  private embeddingSerivce = new CollectionEmbeddingService()

  /**
   * Query Pinecone collections index; return at most 3 per sentence.
   */
  async query(description: string, k = 3) {
    const matches = await this.embeddingSerivce.query(description, k)
    return matches.map((match) => ({
      id: match.id || '',
      score: match.score,
      metadata: match.metadata ?? {},
    }))
  }

}
