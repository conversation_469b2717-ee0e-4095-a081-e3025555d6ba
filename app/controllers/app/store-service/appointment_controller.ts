import { inject } from '@adonisjs/core'
import { HttpContext } from '@adonisjs/core/http'
import AppointmentService from '#services/store_service/appointment_service'
import { EAppointmentStatus } from '#models/store_service/zn_appointment'
import {
  createAppointmentValidator,
  updateAppointmentValidator,
  updateAppointmentStatusValidator,
} from '#validators/store-service/appointment'

@inject()
export default class AppointmentController {
  constructor(protected appointmentService: AppointmentService) {}

  async create({ request, response }: HttpContext) {
    const payload = await request.validateUsing(createAppointmentValidator)
    const appointment = await this.appointmentService.create(payload)
    return response.created(appointment)
  }

  async show({ params, response }: HttpContext) {
    const appointment = await this.appointmentService.findById(params.id)
    return response.ok(appointment)
  }

  async getByStore({ request, params, response }: HttpContext) {
    const storeId = params.storeId
    const queryParams = request.qs()
    const appointments = await this.appointmentService.getByStore(storeId, queryParams)
    return response.ok(appointments)
  }

  async getByCustomer({ request, params, response }: HttpContext) {
    const customerId = params.customerId
    const queryParams = request.qs()
    const appointments = await this.appointmentService.getByCustomer(customerId, queryParams)
    return response.ok(appointments)
  }

  async update({ params, request, response }: HttpContext) {
    const payload = await request.validateUsing(updateAppointmentValidator)
    const appointment = await this.appointmentService.update(params.id, payload)
    return response.ok(appointment)
  }

  async updateStatus({ params, request, response }: HttpContext) {
    const { status } = await request.validateUsing(updateAppointmentStatusValidator)

    if (!Object.values(EAppointmentStatus).includes(status as EAppointmentStatus)) {
      return response.badRequest({ error: 'Invalid appointment status' })
    }

    const appointment = await this.appointmentService.updateStatus(
      params.id,
      status as EAppointmentStatus
    )
    return response.ok(appointment)
  }

  async delete({ params, response }: HttpContext) {
    await this.appointmentService.delete(params.id)
    return response.ok({ message: 'Appointment deleted successfully' })
  }
}
