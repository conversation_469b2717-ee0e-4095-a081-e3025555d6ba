import type {HttpContext} from "@adonisjs/core/http";
import {ZurnoAIReceptionistService} from "#services/zurno_ai_receptionist_service";
import ZnCall from "#models/zn_call";
import OpenAI from "openai";
import ZnCallMessage from "#models/zn_call_message";

export default class AiReceptionistsController {
  private zurnoAIReceptionistService
  private openai
  constructor() {
    this.zurnoAIReceptionistService = new ZurnoAIReceptionistService()
    this.openai = new OpenAI()
  }
  async welcome({ request, response }: HttpContext) {
    const callSid = request.input('CallSid');
    const fromNumber = request.input('From'); // E.g., "+15551234567"
    const toNumber = request.input('To'); // E.g., "+15551234567"

    console.log('Incoming call from:', fromNumber, toNumber, callSid);

    //Search store by to number
    //Search customer by from number
    //Lookup previous thread
    let threadId
    const previousCall = await ZnCall.query().where({
          fromNumber,
          toNumber
        }).first()

    if(previousCall) {
      threadId = previousCall.threadId
    } else {
      const thread = await this.openai.beta.threads.create()
      threadId = thread.id
    }
    console.log('threadId:', threadId);

    //Create call room
    await ZnCall.create({
      fromNumber: fromNumber,
      toNumber: toNumber,
      clientCallId: callSid,
      threadId
    })
    //Create thread
    //Add welcome message
    const welcomeMessage = 'Hi, I am Zurno AI Receptionist. How can i help you?.'
    const welcome = this.zurnoAIReceptionistService.sayAndListen(welcomeMessage)

    response.type('text/xml');
    response.send(welcome);
  }

  async processFlow({ request, response }: HttpContext) {
    const callSid = request.input('CallSid');
    // const messageId = request.input('id');
    const room = await ZnCall.query().where('clientCallId', callSid).first()
    let answer = 'Sorry, we are not able to talk'

    if(room) {
      const recordingUrl = request.input('RecordingUrl') + '.wav'; // Twilio gives .mp3, use .wav for Whisper
      console.log(callSid, recordingUrl)

      const userSaid = await this.zurnoAIReceptionistService.translateVoiceToText(recordingUrl)
      console.log('Caller said:', userSaid);
      //Create questions then insert into db
      const userMessage = await ZnCallMessage.create({
        callId: room?.id,
        message: userSaid
      })

      if (userSaid.toLowerCase().includes('appointment')) {
        answer = this.zurnoAIReceptionistService.handleMakeAppointment()
      } else if (userSaid.toLowerCase().includes('bye')) {
        answer = this.zurnoAIReceptionistService.handleBye()
      } else if (userSaid.toLowerCase().includes('agent')) {
        answer = this.zurnoAIReceptionistService.redialToAgent()
      } else {
        // Start Zurno Customer service
        // Start AI handle and save into db
        this.zurnoAIReceptionistService.handleReception(room, userMessage, userSaid)
        //Pause 5s
        answer = this.zurnoAIReceptionistService.handlePause(userMessage)
      }
    }
    response.type('text/xml');
    response.send(answer);
  }

  async processReceptionist({ params, response }: HttpContext) {
    const messageId = params.id
    console.log(messageId)
    //Getting AI answer
    const aiResponse = messageId ? await ZnCallMessage.query().where('id', messageId).first() : null
    let answer = 'Sorry'
    if(aiResponse) {
      if(aiResponse.reply) { //If ready
        await this.zurnoAIReceptionistService.sayAndListen(aiResponse.reply)
        answer = this.zurnoAIReceptionistService.handleLoop()
      } else {
        //Wait more 5s
        answer = this.zurnoAIReceptionistService.handlePause(aiResponse)
      }
    }

    response.type('text/xml');
    response.send(answer);
  }
}
