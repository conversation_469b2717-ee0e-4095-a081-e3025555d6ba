import ZnChatRoom from '#models/zn_chat_room'
import ZnChatThread from '#models/zn_chat_thread'
import { IvsChatBotService } from '#services/aws/ivschat_bot_service'
import { Job } from '@rlanz/bull-queue'
import ZurnoAssistantService from '#services/zurno_assistant_service'
import { CHAT_EVENT } from '#constants/stream_events'
import {AgentInit, AgentResponse} from '#services/chatbot/chatbot_agent_interface'

interface ChatBotMessageJobPayload {
  roomId: string
  data: { content: string }
  userId: string
}

export default class ChatBotMessageJob extends Job {
  private ivsChatBotService = new IvsChatBotService()
  private zurnoService = new ZurnoAssistantService()

  static get $$filepath() {
    return import.meta.url
  }

  async handle({ roomId, userId, data }: ChatBotMessageJobPayload) {
    /* ---------- Finding possible room ---------- */
    const [room, thread] = await Promise.all([
      ZnChatRoom.find(roomId),
      ZnChatThread.findBy({ roomId }),
    ])

    if (!room || !thread) {
      return this.logger.error('Chat Room Not Found')
    }

    const init: AgentInit = {
      userId,
      threadId: thread.openaiThreadId,
      userMessage: data.content,
    }

    const { firstMessage, needsSecond, _agent } = await this.zurnoService.getInitialMessage(init)

    /* ---------- Branch 1: simple case (no second phase) ---------- */

    if (!needsSecond) {
      await this.sendChatMessage(room, firstMessage)
      return
    }

    /* ---------- Branch 2: we need a post-processing pass --------- */

    await this.sendChatMessage(room, {
      text : firstMessage.text,
      assistantId : firstMessage.assistantId,
      questions : [],
      productIds : [],
      collectionIds : []
    })

    console.log("Sending event to IVS")
    await this.ivsChatBotService.sendEvent(room.arn, CHAT_EVENT.FINDING_PRODUCT)
    console.log("Finished sending event to IVS")

    console.log("Getting second response from agent", _agent.role)
    const finalResponse: AgentResponse = await this.zurnoService.getPostProcessed(init, _agent, firstMessage)
    const finalMessage : AgentResponse = {
      ...finalResponse,
      text: 'FINDING PRODUCTS',
      questions: firstMessage.questions,
      assistantId: firstMessage.assistantId,
    }
    await this.sendChatMessage(room, finalMessage)
  }

  private async sendChatMessage(
    room: any,
    message: {
      text: string
      assistantId: string
      productIds?: string[]
      collectionIds?: string[]
      questions?: string[]
    }
  ) {
    console.log("Sending message to IVS: ", {
      roomArn: room.arn,
      assistantId: message.assistantId,
      content: message.text,
      attributes: {
        productIds: JSON.stringify(message.productIds ?? []),
        collectionIds: JSON.stringify(message.collectionIds ?? []),
        postIds: JSON.stringify([]),
        questions: JSON.stringify(message.questions ?? []),
      },
    })
    await this.ivsChatBotService.sendChatMessage({
      roomArn: room.arn,
      assistantId: message.assistantId,
      content: message.text,
      attributes: {
        productIds: JSON.stringify(message.productIds ?? []),
        collectionIds: JSON.stringify(message.collectionIds ?? []),
        postIds: JSON.stringify([]),
        questions: JSON.stringify(message.questions ?? []),
      },
    })
  }

  async rescue() {}
}
