import ZnChatRoom from '#models/zn_chat_room'
import ZnChatThread from '#models/zn_chat_thread'
import { IvsChatBotService } from '#services/aws/ivschat_bot_service'
import { Job } from '@rlanz/bull-queue'
import ZurnoAssistantService from '#services/zurno_assistant_service'
import { CHAT_EVENT } from '#constants/stream_events'
import {AgentInit, AgentResponse} from '#services/chatbot/chatbot_agent_interface'
import {EAIAssistantRole} from "#models/zn_ai_assistant";

interface ChatBotMessageJobPayload {
  roomId: string
  data: { content: string }
  userId: string
}

export default class ChatBotMessageJob extends Job {
  private ivsChatBotService = new IvsChatBotService()
  private zurnoService = new ZurnoAssistantService()

  static get $$filepath() {
    return import.meta.url
  }

  async handle({ roomId, userId, data }: ChatBotMessageJobPayload) {
    /* ---------- Finding possible room ---------- */
    const [room, thread] = await Promise.all([
      ZnChatRoom.find(roomId),
      ZnChatThread.findBy({ roomId }),
    ])

    if (!room || !thread) {
      return this.logger.error('Chat Room Not Found')
    }

    const init: AgentInit = {
      userId,
      threadId: thread.openaiThreadId,
      userMessage: data.content,
    }

    const { firstMessage, needsSecond, _agent, nextAgent } = await this.zurnoService.getInitialMessage(init, roomId)


    if (nextAgent) {
      await this.sendChatMessage(room, {
        ...firstMessage,
        nextAgentRole: nextAgent.role
      })
      return
    }
    /* ---------- Branch 1: simple case (no second phase) ---------- */

    if (!needsSecond) {
      await this.sendChatMessage(room, firstMessage)
      return
    }

    /* ---------- Branch 2: we need a post-processing pass --------- */

    await this.sendChatMessage(room, {
      text : firstMessage.text,
      assistantId : firstMessage.assistantId,
      questions : [],
      productIds : [],
      collectionIds : []
    })

    await this.ivsChatBotService.sendEvent(room.arn, CHAT_EVENT.FINDING_PRODUCT)

    const finalResponse: AgentResponse = await this.zurnoService.getPostProcessed(init, _agent, firstMessage)
    const finalMessage : AgentResponse = {
      ...finalResponse,
      text: 'FINDING PRODUCTS',
      questions: firstMessage.questions,
      assistantId: firstMessage.assistantId,
    }
    await this.sendChatMessage(room, finalMessage)
  }

  private async sendChatMessage(
    room: any,
    message: {
      text: string
      assistantId: string
      productIds?: string[]
      collectionIds?: string[]
      postIds?: string[]
      questions?: string[]
      nextAgentRole?: EAIAssistantRole
    }
  ) {
    console.log("Sending this message to IVS: ", {
      roomArn: room.arn,
      assistantId: message.assistantId,
      content: message.text,
      attributes: {
        productIds: JSON.stringify(message.productIds ?? []),
        collectionIds: JSON.stringify(message.collectionIds ?? []),
        postIds: JSON.stringify(message.postIds ?? []),
        questions: JSON.stringify(message.questions ?? []),
        ...(message.nextAgentRole && {nextAgent: message.nextAgentRole}),
      }
    })
     await this.ivsChatBotService.sendChatMessage({
      roomArn: room.arn,
      assistantId: message.assistantId,
      content: message.text,
      attributes: {
        productIds: JSON.stringify(message.productIds ?? []),
        collectionIds: JSON.stringify(message.collectionIds ?? []),
        postIds: JSON.stringify(message.postIds ?? []),
        questions: JSON.stringify(message.questions ?? []),
        ...(message.nextAgentRole && {nextAgent: message.nextAgentRole}),
      },
    })
  }

  async rescue() {}
}
