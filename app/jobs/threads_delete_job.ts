import {Job} from "@rlanz/bull-queue";
import OpenA<PERSON> from "openai";
import ZnChatThread from "#models/zn_chat_thread";
import {DateTime} from "luxon";

interface ThreadsDeleteJobPayLoad {
  threadIds : string[]
}

export default class Threads<PERSON><PERSON><PERSON>Job extends Job{
  private openai = new OpenAI()
  static get $$filepath() {
    return import.meta.url
  }

  private async deleteAndReplaceOldThread(thread: ZnChatThread) {
    const newThread = await this.openai.beta.threads.create()
    const oldThreadId = thread.openaiThreadId
    thread.openaiThreadId = newThread.id
    thread.updatedAt = DateTime.now()
    try {
      await this.openai.beta.threads.del(oldThreadId)
    } catch (error) {
      this.logger.error(error, 'Error found' )
    }

    await thread.save()
  }

  async handle(payload: ThreadsDeleteJobPayLoad) {
    const chatThreads = await ZnChatThread.query().whereIn('id', payload.threadIds)
    const batchSize = 10
    for (let index = 0; index < chatThreads.length; index += batchSize) {
      const batch = chatThreads.slice(index, index + batchSize)
      await Promise.all(
        batch.map( async (thread : ZnChatThread) => {
          try {
            await  this.deleteAndReplaceOldThread(thread)
          } catch (error) {
          }
        })
      )
    }
  }

  async rescue() {}
}
