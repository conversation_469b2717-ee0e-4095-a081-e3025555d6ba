import ZnOrder from '#models/zn_order'
import { Job } from '@rlanz/bull-queue'
import { DateTime } from 'luxon'

interface SyncOrderCancelledJobPayload {
  orders: any
}

export default class SyncOrderCancelled<PERSON>ob extends Job {
  // This is the path to the file that is used to create the job
  static get $$filepath() {
    return import.meta.url
  }

  /**
   * Base Entry point
   */
  async handle(payload: SyncOrderCancelledJobPayload) {
    for (const order of payload.orders) {
      const dataOrder = await ZnOrder.findBy({ shopifyId: order.id })
      if (!dataOrder) continue

      await dataOrder
        .merge({
          cancelledAt: order.cancelledAt
            ? (DateTime.fromISO(order.cancelledAt).toFormat('yyyy-MM-dd HH:mm:ss') as any)
            : dataOrder.cancelledAt,
          status: order.cancelledAt ? 'cancel' : dataOrder.status,
          financialStatus: order.displayFinancialStatus?.toLowerCase() || dataOrder.financialStatus,
        })
        .save()
    }
  }

  /**
   * This is an optional method that gets called when the retries has exceeded and is marked failed.
   */
  async rescue(_: SyncOrderCancelledJobPayload) {}
}
