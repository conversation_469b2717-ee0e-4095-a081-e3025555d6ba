import { Job } from '@rlanz/bull-queue'
import queue from '@rlanz/bull-queue/services/main'
import OpenAI from 'openai'
import UpdateVectorStorePrepareJob from './update_vector_store_prepare_job.js'

interface UpdateVectorStoreJobPayload {
  vectorStoreId: string
}

export default class UpdateVectorStoreJob extends Job {

  // This is the path to the file that is used to create the job
  static get $$filepath() {
    return import.meta.url
  }

  /**
   * Base Entry point
   */
  async handle(payload: UpdateVectorStoreJobPayload) {
    const { vectorStoreId } = payload

    const openai = new OpenAI()
    //
    // // delete openai files
    // let openaiFilesHasMore = true
    // let openaiFilePage = 1

    // while (openaiFilesHasMore) {
    //   const fileResponse = await openai.files.list()
    //   openaiFilesHasMore = fileResponse.has_more
    //
    //   for (const file of fileResponse.data) {
    //     await openai.files.del(file.id)
    //   }
    //
    //   this.logger.info("OpenAI files deleted: Page " + openaiFilePage);
    //
    //   openaiFilePage++
    // }
    this.logger.info("OpenAI files deleted: All");

    //delete openai vector files
    let openaiVectorFilesHasMore = true
    let openaiVectorFilePage = 1

    while (openaiVectorFilesHasMore) {
      const vectorfileResponse = await openai.vectorStores.files.list(vectorStoreId)
      openaiVectorFilesHasMore = vectorfileResponse.has_more

      for (const file of vectorfileResponse.data) {
        await openai.vectorStores.files.del(vectorStoreId, file.id)
      }

      this.logger.info("OpenAI vector files deleted: Page " + openaiVectorFilePage);

      openaiVectorFilePage++
    }
    this.logger.info("OpenAI vector files deleted: All");

    // add product vector files
    await queue.dispatch(
      UpdateVectorStorePrepareJob,
      {
        vectorStoreId,
        resource: 'products'
      },
      { queueName: 'syncData' }
    )
    this.logger.info('UpdateVectorStorePrepareJob Products Sent')

    // add collection vector files
    await queue.dispatch(
      UpdateVectorStorePrepareJob,
      {
        vectorStoreId,
        resource: 'collections'
      },
      { queueName: 'syncData' }
    )
    this.logger.info('UpdateVectorStorePrepareJob Collections Sent')

    await queue.dispatch(
      UpdateVectorStorePrepareJob,
      {
        vectorStoreId,
        resource: 'documents'
      },
      { queueName: 'syncData' }
    )
    this.logger.info('UpdateVectorStorePrepareJob Documents Sent')

    // add post vector files
    // await queue.dispatch(
    //   UpdateVectorStorePrepareJob,
    //   {
    //     vectorStoreId,
    //     resource: 'posts'
    //   },
    //   { queueName: 'syncData' }
    // )
    this.logger.info('UpdateVectorStorePrepareJob Posts Sent')
  }

  /**
   * This is an optional method that gets called when the retries has exceeded and is marked failed.
   */
  async rescue(_: UpdateVectorStoreJobPayload) { }
}
