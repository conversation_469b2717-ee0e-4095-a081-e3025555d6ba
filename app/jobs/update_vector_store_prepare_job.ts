import ZnProduct from '#models/zn_product'
import { Job } from '@rlanz/bull-queue'
import queue from '@rlanz/bull-queue/services/main'
import UpdateVectorStoreFileJob from './update_vector_store_file_job.js'
import ZnCollection from '#models/zn_collection'
import ZnPost from '#models/zn_post'
import fs   from 'node:fs/promises'
import path from 'node:path'


interface UpdateVectorStorePrepareJobPayload {
  vectorStoreId: string,
  resource: 'products' | 'collections' | 'posts' | 'documents'
}

export default class UpdateVectorStorePrepareJob extends Job {

  // This is the path to the file that is used to create the job
  static get $$filepath() {
    return import.meta.url
  }

  /**
   * Base Entry point
   */
  async handle(payload: UpdateVectorStorePrepareJobPayload) {
    const { vectorStoreId, resource } = payload

    const root = process.cwd()

    switch (resource) {
      case 'products': {
        const limit = 500
        let hasMore = true
        let page = 1

        while (hasMore) {
          const products = await ZnProduct.query()
            .preload('category')
            .preload('collections')
            .preload('productType')
            .preload('tags')
            .preload('reviewsSummary')
            .preload('variants')
            .preload('variant', (query) => {
              query.preload('image').preload('optionValues')
            })
            .paginate(page, limit)

          const meta = products.serialize().meta
          hasMore = meta.nextPageUrl

          const data = products.serialize().data

          await queue.dispatch(
            UpdateVectorStoreFileJob,
            {
              vectorStoreId,
              resource: 'products',
              data,
              meta,
            },
            { queueName: 'syncData' }
          )
          this.logger.info(
            `UpdateVectorStoreFileJob Products - Page ${meta.currentPage} / ${meta.lastPage} - Sent`
          )

          page++
        }
        break
      }

      case 'collections': {
        const limit = 300
        let hasMore = true
        let page = 1

        while (hasMore) {
          const collections = await ZnCollection.query()
            .where('status', true)
            .preload('image')
            .preload('categories')
            // .preload('products')
            .paginate(page, limit)

          const meta = collections.serialize().meta
          hasMore = meta.nextPageUrl

          const data = collections.serialize().data

          await queue.dispatch(
            UpdateVectorStoreFileJob,
            {
              vectorStoreId,
              resource: 'collections',
              data,
              meta,
            },
            { queueName: 'syncData' }
          )
          this.logger.info(
            `UpdateVectorStoreFileJob Collections - Page ${meta.currentPage} / ${meta.lastPage} - Sent`
          )

          page++
        }
        break
      }

      case 'posts': {
        const limit = 500
        let hasMore = true
        let page = 1

        while (hasMore) {
          const posts = await ZnPost.query()
            .where('expired', false)
            .where('isUnlist', false)
            .where('isDraft', false)
            .preload('store', (storeQuery) => {
              storeQuery.preload('logo')
            })
            .preload('categories', (categoryQuery) => {
              categoryQuery.preload('thumbnail')
            })
            .preload('thumbnail')
            .preload('medias')
            .preload('product', (productQuery) => {
              productQuery.preload('variant').preload('image').where('status', 'active')
            })
            .paginate(page, limit)

          const meta = posts.serialize().meta
          hasMore = meta.nextPageUrl

          const data = posts.serialize().data

          await queue.dispatch(
            UpdateVectorStoreFileJob,
            {
              vectorStoreId,
              resource: 'posts',
              data,
              meta,
            },
            { queueName: 'syncData' }
          )
          this.logger.info(
            `UpdateVectorStoreFileJob Posts - Page ${meta.currentPage} / ${meta.lastPage} - Sent`
          )

          page++
        }
        break
      }

      case 'documents':
        const documentsDirectory = path.join(root, 'app', 'services', 'chatbot', 'documents')
        const documentsPaths : string[] = (await fs.readdir(documentsDirectory))
          .filter(file => file.toLowerCase().endsWith('.pdf'))
          .map(file => path.join(documentsDirectory, file))

        if (documentsPaths.length === 0) {
          this.logger.warn(`No PDFs found in ${documentsDirectory}`)
          break
        }

        const meta = {currentPage: 1, lastPage: 1}

        await queue.dispatch(
          UpdateVectorStoreFileJob,
          {
            vectorStoreId, resource : 'documents', data: documentsPaths, meta
          },
          { queueName: 'syncData' }
        )

        this.logger.info(`UpdateVectorStoreFileJob Documents – ${documentsPaths.length} PDFs queued`)
        break
    }
  }

  /**
   * This is an optional method that gets called when the retries has exceeded and is marked failed.
   */
  async rescue(_: UpdateVectorStorePrepareJobPayload) { }
}
