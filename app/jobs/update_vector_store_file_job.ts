import ZnCollection from '#models/zn_collection'
import ZnPost from '#models/zn_post'
import ZnProduct from '#models/zn_product'
import { Job } from '@rlanz/bull-queue'
import OpenAI from 'openai'
import {htmlToText} from "../../services/commons.js";
import {fileFromPath} from "openai";
import path from 'node:path'

interface UpdateVectorStoreFileJobPayload {
  vectorStoreId: string,
  resource: 'products' | 'collections' | 'posts' | 'documents'
  data: any[],
  meta: any
}

export default class UpdateVectorStoreFileJob extends Job {
  private openai = new OpenAI()

  // This is the path to the file that is used to create the job
  static get $$filepath() {
    return import.meta.url
  }

  /**
   * Base Entry point
   */
  async handle(payload: UpdateVectorStoreFileJobPayload) {
    const { vectorStoreId, resource, meta } = payload

    let formattedData
    switch (resource) {
      case ('products'): {
        const data = payload.data as ZnProduct[]

        formattedData = data.map(prod => ({
          type: "product",

          title: prod.title,
          description: htmlToText(prod.description || ''),

          price: prod.price,
          compareAtPrice: prod.compareAtPrice,

          category: prod.category?.name,
          productType: prod.productType?.name,
          tags: prod.tags?.map(tag => tag.name),
          collections: prod.collections?.map(col => col.title),
          sku: prod.variants?.map(variant => variant.sku),
          created_at: prod.createdAt,
          updated_at: prod.updatedAt,

          reviewSummary: {
            averageRating: prod.reviewSummary?.averageRating,
            totalReviews: prod.reviewSummary?.totalReviews,
          },

          variants: prod.variants?.map(variant => ({
            title: variant.title,
            price: variant.price,
            compareAtPrice: variant.compareAtPrice,
            sku: variant.sku,
            image: variant.image?.src,
            stock: variant.inventoryQuantity,
            attributes: variant.optionValues?.map(option => option.value)
          }))

        }))

        break
      }

      case ('collections'): {
        const data = payload.data as ZnCollection[]

        formattedData = data.map(col => ({
          type: "collection",

          title: col.title,
          description: col.description,

          image: col.imageUrl || col.image?.url,

          categories: col.categories?.map(cat => ({
            name: cat.name,
            fullName: cat.fullName,
          })),

          products: col.products?.map(prod => ({
            // handle: prod.handle,
            type: "product",
            productId: prod.id,

            title: prod.title,
            description: htmlToText(prod.description || ''),

            price: prod.price,
            compareAtPrice: prod.compareAtPrice,
          })),

        }))

        break
      }

      case ('posts'): {
        const data = payload.data as ZnPost[]

        formattedData = data.map(post => ({
          type: "post",

          title: post.title,
          description: post.description,

          price: post.price,
          url: post.url,

          tags: post.tags,

          latitude: post.latitude,
          longitude: post.longitude,

          contactName: post.contactName,
          phone: post.phone,
          email: post.email,

          address: post.address,
          address2: post.address2,
          zipcode: post.zipcode,


          images: [post.thumbnail?.url, ...post.medias.map(media => media.url)].filter(Boolean),

          categories: post.categories.map(cat => cat.name),

          store: {
            name: post.store?.name,
            phoneNumber: post.store?.phoneNumber,
            email: post.store?.email,
          }

        }))

        break
      }

      case ('documents') : {
        const documentsPaths = payload.data as string[]
        for (const docPath of documentsPaths) {
          try {
            const file = await fileFromPath(docPath)
            const response = await this.openai.vectorStores.files.uploadAndPoll(vectorStoreId, file)
            const message = `PDF ${path.basename(docPath)} - ${response.status}`
            response.status === 'completed' ? this.logger.info(message) : this.logger.error(message)
          } catch (error) {
            this.logger.error(`Failed to upload ${docPath}`, error)
          }
        }
      }

      return

    }

    const jsonString = JSON.stringify(formattedData, null, 2)
    const jsonFile = new File([jsonString], `zurno-${resource}-${meta.currentPage}.json`, { type: 'application/json' });
    // fs.writeFileSync(`./data/zurno-${resource}-${meta.currentPage}.json`, JSON.stringify(formattedData, null, 2));
    // console.log(jsonString)

    const response = await this.openai.vectorStores.files.uploadAndPoll(vectorStoreId, jsonFile)

    const responseMessage = `OpenAI ${resource} vector files upload: Page ${meta.currentPage} / ${meta.lastPage} - Status: ${response.status}`
    if (response.status == 'completed' || response.status == 'in_progress') {
      this.logger.info(responseMessage)
    } else {
      this.logger.error(responseMessage)
    }
  }

  /**
   * This is an optional method that gets called when the retries has exceeded and is marked failed.
   */
  async rescue(_: UpdateVectorStoreFileJobPayload) { }
}
