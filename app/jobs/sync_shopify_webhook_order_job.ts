import ZnOrder from '#models/zn_order'
import { AffiliationCommissionService } from '#services/affiliation/affiliation_commission_service'
import { AffiliationService } from '#services/affiliation/affiliation_service'
import { GiftService } from '#services/shop/gift_service'
import { OrderService } from '#services/shop/order_service'
import { SyncOrderService } from '#services/sync/sync_order_service'
import { Job } from '@rlanz/bull-queue'

interface SyncShopifyWebhookOrderJobPayload {
  order: any
}

export default class SyncShopifyWebhookOrderJob extends Job {
  syncOrderService = new SyncOrderService()
  affiliateService = new AffiliationService()
  commissionService = new AffiliationCommissionService()
  orderService = new OrderService()

  // This is the path to the file that is used to create the job
  static get $$filepath() {
    return import.meta.url
  }

  /**
   * Base Entry point
   */
  async handle(payload: SyncShopifyWebhookOrderJobPayload) {
    // @ts-ignore
    const updatingOrder = await ZnOrder.query({ mode: 'write' })
      .where('shopifyId', payload.order.admin_graphql_api_id)
      .first()

    const isOrderCreating = await this.syncOrderService.isOrderCreating(updatingOrder)
    const isOrderRefunding = await this.syncOrderService.isOrderRefunding(updatingOrder, payload.order)
    const isOrderCancelling = await this.syncOrderService.isOrderCancelling(updatingOrder, payload.order)

    if (isOrderRefunding) await this.commissionService.preUpdateCommissionOnOrderRefunded(updatingOrder)

    await this.syncOrderService.syncOrderFromShopifyWebhook(payload.order)

    if (isOrderCreating) await this.affiliateService.verifyAppliedDiscountCode(payload.order)
    if (isOrderRefunding) {
      await this.commissionService.postUpdateCommissionOnOrderRefunded(payload.order.id)
      await this.orderService.notifyAdminOnOrderRefunding(payload.order)
    }
    if (isOrderCancelling) {
      await this.commissionService.updateCommissionOnOrderCancelled(payload.order.id)

      // Cancel gift if order is cancelled
      if (updatingOrder && updatingOrder.userId && updatingOrder.id) {
        const giftService = new GiftService()
        await giftService.cancelGift({ userId: updatingOrder.userId, orderId: updatingOrder.id })
      }
    }
  }

  /**
   * This is an optional method that gets called when the retries has exceeded and is marked failed.
   */
  async rescue(_: SyncShopifyWebhookOrderJobPayload) { }
}
