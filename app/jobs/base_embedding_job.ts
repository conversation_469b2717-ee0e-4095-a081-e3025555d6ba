import {Job} from "@rlanz/bull-queue";
import {PineconeEmbeddingService} from "#services/pinecone/pinecone_embedding_service";

export abstract class BaseEmbeddingJob <
  TModel,
  TPayload extends  { ids?: string | string[] } = {ids? : string | string[] }
> extends Job {

  static get $$filepath() { return import.meta.url }
  protected abstract embeddingService: PineconeEmbeddingService<TModel>
  protected abstract lookup(ids: string[]): Promise<TModel[]>

  async handle( {ids }: TPayload) {
    const idsArray = ids ? (Array.isArray(ids) ? ids : [ids]) : []
    if (idsArray.length === 0) return

    const records = await this.lookup(idsArray)
    if (records.length > 0) await  this.embeddingService.upsert(records)

  }

  async rescue() {}
}

