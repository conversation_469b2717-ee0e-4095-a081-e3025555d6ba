import vine from '@vinejs/vine'

export const createAppointmentValidator = vine.compile(
  vine.object({
    storeId: vine.string().uuid(),
    customerId: vine.string().uuid(),
    status: vine.string().optional(),
    startTime: vine.date().optional(),
    endTime: vine.date().optional(),
    notes: vine.string().optional(),
    taxId: vine.string().uuid().optional(),
    services: vine.array(vine.string().uuid()).optional(),
    packages: vine.array(vine.string().uuid()).optional(),
  })
)

export const updateAppointmentValidator = vine.compile(
  vine.object({
    storeId: vine.string().uuid().optional(),
    customerId: vine.string().uuid().optional(),
    status: vine.string().optional(),
    startTime: vine.date().optional(),
    endTime: vine.date().optional(),
    notes: vine.string().optional(),
    taxId: vine.string().uuid().optional(),
    services: vine.array(vine.string().uuid()).optional(),
    packages: vine.array(vine.string().uuid()).optional(),
  })
)

export const updateAppointmentStatusValidator = vine.compile(
  vine.object({
    status: vine.string().optional(),
  })
)
