import { column, manyToMany } from '@adonisjs/lucid/orm'
import type { ManyToMany } from '@adonisjs/lucid/types/relations'
import AppModel from './app_model.js'
import ZnProductVariant from './zn_product_variant.js'

export default class ZnBundleCollectionItem extends AppModel {
  @column({ columnName: 'bundleId' })
  declare bundleId: string

  @column({ columnName: 'bundleCollectionId' })
  declare bundleCollectionId: string

  @column({ columnName: 'fastBundleId' })
  declare fastBundleId: string

  @column({ columnName: 'productId' })
  declare productId: string

  @column({ columnName: 'title' })
  declare title: string

  @column({ columnName: 'image' })
  declare image: string | null

  @manyToMany(() => ZnProductVariant, {
    pivotTable: 'zn_bundle_collection_item_variants',
    pivotForeignKey: 'bundleCollectionItemId',
    pivotRelatedForeignKey: 'variantId',
  })
  declare variants: ManyToMany<typeof ZnProductVariant>
}
