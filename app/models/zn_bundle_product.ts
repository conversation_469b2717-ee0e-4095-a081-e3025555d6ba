import { belongsTo, column, hasMany, hasOne } from '@adonisjs/lucid/orm'
import type { BelongsTo, HasMany, HasOne } from '@adonisjs/lucid/types/relations'
import AppModel from './app_model.js'
import ZnBundleCollection from './zn_bundle_collection.js'
import ZnBundleProductDiscount from './zn_bundle_product_discount.js'
import ZnBundleProductItem from './zn_bundle_product_item.js'
import ZnProduct from './zn_product.js'

export default class ZnBundleProduct extends AppModel {
  static table = 'zn_bundle_products'

  @column({ columnName: 'fastBundleId' })
  declare fastBundleId: string | null

  @column({ columnName: 'fastBundleUuid' })
  declare fastBundleUuid: string | null

  @column({ columnName: 'title' })
  declare title: string

  @column({ columnName: 'type' })
  declare type: string | null

  @column({ columnName: 'isActive' })
  declare isActive: boolean

  @column({ columnName: 'bapId' })
  declare bapId: string | null

  @column({ columnName: 'mainProductId' })
  declare mainProductId: string | null

  @column({ columnName: 'hasQuantityCap' })
  declare hasQuantityCap: boolean

  @column({ columnName: 'itemType' })
  declare itemType: string | null

  @hasMany(() => ZnBundleProductItem, {
    foreignKey: 'bundleProductId',
  })
  declare items: HasMany<typeof ZnBundleProductItem>

  @hasMany(() => ZnBundleProductDiscount, {
    foreignKey: 'bundleProductId',
  })
  declare discounts: HasMany<typeof ZnBundleProductDiscount>

  @hasOne(() => ZnBundleProductDiscount, {
    foreignKey: 'bundleProductId',
  })
  declare discount: HasOne<typeof ZnBundleProductDiscount>

  @belongsTo(() => ZnProduct, {
    foreignKey: 'mainProductId',
  })
  declare mainProduct: BelongsTo<typeof ZnProduct>

  @hasMany(() => ZnBundleCollection, {
    foreignKey: 'bundleId',
    onQuery: (query) => {
      query.where('isActive', true).orderBy('ignoresDiscount')
    },
  })
  declare collections: HasMany<typeof ZnBundleCollection>
}
