import AppModel from "#models/app_model";
import { column } from "@adonisjs/lucid/orm";

export enum EAIAssistantRole {
  CUSTOMER_SERVICE = 'customer_service',
  SHOPPING_ASSISTANT = 'shopping_assistant',
  ORDER_ASSISTANT = 'order_assistant',
  RECEPTIONIST_SERVICE = 'receptionist_service',
  POST_ASSISTANT = 'post_assistant',
}

export default class ZnAIAssistant extends AppModel {
  @column({ columnName: 'openaiAssistantId' })
  declare openaiAssistantId: string

  @column()
  // @no-swagger
  declare role: EAIAssistantRole
}
