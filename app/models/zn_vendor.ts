import { column, has<PERSON>any, manyToMany } from '@adonisjs/lucid/orm'
import type { HasMany, ManyToMany } from '@adonisjs/lucid/types/relations'
import AppModel from './app_model.js'
import ZnProduct from './zn_product.js'
import ZnUser from './zn_user.js'
import ZnMedia from './zn_media.js'
import { EApprovalStatus } from '#constants/approval_status'

export default class ZnVendor extends AppModel {
  static table = 'zn_vendors'

  @column({ columnName: 'companyName' })
  declare companyName: string

  @column({ columnName: 'brandName' })
  declare brandName: string

  @column({ columnName: 'website' })
  declare website: string

  @column({ columnName: 'contactName' })
  declare contactName: string

  @column({ columnName: 'phone' })
  declare phone: string

  @column({ columnName: 'email' })
  declare email: string

  @column({ columnName: 'address1' })
  declare address1: string

  @column({ columnName: 'address2' })
  declare address2: string

  @column({ columnName: 'city' })
  declare city: string

  @column({ columnName: 'state' })
  declare state: string

  @column({ columnName: 'country' })
  declare country: string

  @column({ columnName: 'zipCode' })
  declare zipCode: string

  @column({ columnName: 'ein' })
  declare ein: string

  @column({ columnName: 'registrationStatus' })
  declare registrationStatus: EApprovalStatus

  @column({ columnName: 'rejectionReason' })
  declare rejectionReason: string | null

  @column({
    columnName: 'commissionRate',
    consume: (value: string) => parseFloat(value)
  })
  declare commissionRate: number

  @column({
    columnName: 'fixedCommissionAmount',
    consume: (value: string) => parseFloat(value)
  })
  declare fixedCommissionAmount: number

  @manyToMany(() => ZnMedia, {
    pivotTable: 'zn_vendors_medias',
    pivotForeignKey: 'vendorId',
    pivotRelatedForeignKey: 'mediaId',
  })
  declare businessLicenseDocuments: ManyToMany<typeof ZnMedia>

  @hasMany(() => ZnProduct)
  declare products: HasMany<typeof ZnProduct>

  @hasMany(() => ZnUser, {
    foreignKey: 'vendorId'
  })
  declare users: HasMany<typeof ZnUser>

  serializeExtras() {
    return {
      // content: this.content !== '' ? this.content : `${this.title} ${this.description}`
      name: this.companyName || 'Zurno'
    }
  }
}
