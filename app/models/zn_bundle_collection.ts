import { belongsTo, column, hasMany } from '@adonisjs/lucid/orm'
import type { BelongsTo, HasMany } from '@adonisjs/lucid/types/relations'
import AppModel from './app_model.js'
import ZnBundleCollectionItem from './zn_bundle_collection_item.js'
import ZnBundleProduct from './zn_bundle_product.js'

export default class ZnBundleCollection extends AppModel {
  @column({ columnName: 'bundleId' })
  declare bundleId: string

  @column({ columnName: 'fastBundleId' })
  declare fastBundleId: string

  @column({ columnName: 'title' })
  declare title: string

  @column({ columnName: 'sectionTitle' })
  declare sectionTitle: string | null

  @column({ columnName: 'sectionDescription' })
  declare sectionDescription: string | null

  @column({ columnName: 'image' })
  declare image: string | null

  @column({ columnName: 'productCount' })
  declare productCount: number

  @column({ columnName: 'showInPage' })
  declare showInPage: boolean

  @column({ columnName: 'plusOneQuantity' })
  declare plusOneQuantity: boolean

  @column({ columnName: 'quantity' })
  declare quantity: number

  @column({ columnName: 'maxQuantity' })
  declare maxQuantity: number

  @column({ columnName: 'ignoresDiscount' })
  declare ignoresDiscount: boolean

  @column({ columnName: 'isActive' })
  declare isActive: boolean

  @belongsTo(() => ZnBundleProduct, {
    foreignKey: 'bundleId',
  })
  declare bundleProduct: BelongsTo<typeof ZnBundleProduct>

  @hasMany(() => ZnBundleCollectionItem, {
    foreignKey: 'bundleCollectionId',
  })
  declare items: HasMany<typeof ZnBundleCollectionItem>
}
